using System;
using System.Linq;
using System.Runtime.CompilerServices;

namespace AgpBeApi.Azure.Functions.Exceptions
{
    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class BadRequestException : BaseException
    {
        /// <summary>
        /// Eccezione custom. Viene Sollevata quando l'errore è causato da dati errati in ingresso.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="sourceFilePath"></param>
        /// <param name="memberName"></param>
        /// <param name="sourceLineNumber"></param>
        public BadRequestException(string message, [CallerFilePath] string sourceFilePath = "", [CallerMemberName] string memberName = "", [CallerLineNumber] int sourceLineNumber = 0) : base(message)
        {
            this.Source = $"[AN ERROR OCCURRED IN THE FUNCTION]: {sourceFilePath?.Split('\\').Last()}_{memberName}_line_{sourceLineNumber}";
        }

        /// <summary>
        /// Eccezione custom. Viene Sollevata quando l'errore è causato da dati errati in ingresso.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        /// <param name="sourceFilePath"></param>
        /// <param name="memberName"></param>
        /// <param name="sourceLineNumber"></param>
        public BadRequestException(string message, Exception innerException, [CallerFilePath] string sourceFilePath = "", [CallerMemberName] string memberName = "", [CallerLineNumber] int sourceLineNumber = 0) : base(message, innerException)
        {
            this.Source = $"[AN ERROR OCCURRED IN THE FUNCTION]: {sourceFilePath?.Split('\\').Last()}_{memberName}_line_{sourceLineNumber}";
        }
    }
}
