using System;

namespace AgpBeApi.Azure.Functions.Exceptions
{
    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public abstract class BaseException : Exception
    {
        #pragma warning disable S3442
        public BaseException(string message) : base(message)
        {

        }

        public BaseException(string message, Exception innerException) : base(message, innerException)
        {

        }
    }
}
