using System;
using System.Linq;
using System.Runtime.CompilerServices;

namespace AgpBeApi.Azure.Functions.Exceptions
{
    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class NotFoundException : BaseException
    {
        /// <summary>
        /// Eccezione custom. Viene Sollevata quando un dato non viene trovato.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="sourceFilePath"></param>
        /// <param name="memberName"></param>
        /// <param name="sourceLineNumber"></param>
        public NotFoundException(string message, [CallerFilePath] string sourceFilePath = "", [CallerMemberName] string memberName = "", [CallerLineNumber] int sourceLineNumber = 0) : base(message)
        {
            this.Source = $"[AN ERROR OCCURRED IN THE FUNCTION]: {sourceFilePath?.Split('\\').Last()}_{memberName}_line_{sourceLineNumber}";
        }

        /// <summary>
        /// Eccezione custom. Viene Sollevata quando un dato non viene trovato.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        /// <param name="sourceFilePath"></param>
        /// <param name="memberName"></param>
        /// <param name="sourceLineNumber"></param>
        public NotFoundException(string message, Exception innerException, [CallerFilePath] string sourceFilePath = "", [CallerMemberName] string memberName = "", [CallerLineNumber] int sourceLineNumber = 0) : base(message, innerException)
        {
            this.Source = $"[AN ERROR OCCURRED IN THE FUNCTION]: {sourceFilePath?.Split('\\').Last()}_{memberName}_line_{sourceLineNumber}";
        }
    }
}
