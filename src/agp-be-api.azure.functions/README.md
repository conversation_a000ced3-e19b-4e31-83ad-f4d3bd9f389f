# AGP BE API Azure Functions

## Overview
This is an Azure Functions project built with .NET 8 that provides a REST API for data management operations. The project uses PostgreSQL as the database and includes Swagger documentation that can be enabled/disabled via configuration.

## Architecture
The project follows a clean architecture pattern with the following structure:
- **Functions**: Azure Functions endpoints (HTTP triggers)
- **Services**: Business logic layer
- **DBSupport**: Data access layer with Entity Framework Core
- **DTO**: Data Transfer Objects
- **Helpers**: Utility classes and extensions

## Features
- Health check endpoint (`/api/v1/health`)
- CRUD operations for sources text records
- Query sources text by user ID, organization ID, or both
- PostgreSQL database support
- Configurable Swagger/OpenAPI documentation
- Environment variable configuration (Kubernetes-ready)
- Advanced error handling with custom exceptions and detailed logging
- JWT token validation and UPN extraction
- Function wrapper for consistent error handling across all endpoints

## API Endpoints

### Health
- `GET /api/v1/health` - Get API health status

### Sources Text Operations
- `POST /api/v1/sources/text` - Create a new sources text record
- `GET /api/v1/sources/text/{id}` - Get sources text record by ID
- `PUT /api/v1/sources/text/{id}` - Update sources text record
- `DELETE /api/v1/sources/text/{id}` - Delete sources text record
- `GET /api/v1/sources/text/user/{userId}` - Get sources text records by user ID
- `GET /api/v1/sources/text/organization/{organizationId}` - Get sources text records by organization ID
- `GET /api/v1/sources/text/user/{userId}/organization/{organizationId}` - Get sources text records by user and organization ID

## Configuration

### Environment Variables
The application uses environment variables for configuration:

#### Database Configuration
- `POSTGRESQL_CONNECTION_STRING` - Full PostgreSQL connection string (optional if individual components are provided)
- `POSTGRESQL_HOST` - PostgreSQL host
- `POSTGRESQL_PORT` - PostgreSQL port (default: 5432)
- `POSTGRESQL_DATABASE` - Database name
- `POSTGRESQL_USERNAME` - Database username
- `POSTGRESQL_PASSWORD` - Database password

#### Application Configuration
- `ENV` - Environment name (Development, Staging, Production)
- `SWAGGER_ENABLED` - Enable/disable Swagger documentation (true/false)
- `APPLICATIONINSIGHTS_CONNECTION_STRING` - Application Insights connection string (optional)
- `REDIS_CONNECTION_STRING` - Redis connection string (optional)

## Database Schema

### sources_text table
- `id_pk` (int, primary key, auto-increment)
- `id_user_fk` (varchar(255), required, indexed)
- `id_organization_fk` (varchar(255), required, indexed)
- `timestamp` (timestamp, required, indexed)
- `title` (varchar(2000), required)
- `text` (text, required)
- `created_at` (timestamp, auto-generated)
- `updated_at` (timestamp, auto-updated)

## Development Setup

1. Install .NET 8 SDK
2. Install Azure Functions Core Tools
3. Set up PostgreSQL database
4. Configure environment variables in `local.settings.json`
5. Run the application:
   ```bash
   func start
   ```

## Deployment
This application is designed to run in Kubernetes containers with environment variables for configuration. No Azure Key Vault dependency is required.

## Error Handling

The application implements a sophisticated error handling system similar to the IUD project:

### Custom Exceptions
- `BadRequestException` - For invalid input data (400)
- `NotFoundException` - For resources not found (404)
- `InternalServerErrorException` - For server errors (500)
- `AuthHeaderNotFoundException` - For missing authentication headers (401)
- `JwtUpnNotFoundException` - For missing UPN in JWT token (401)

### Function Wrapper
All Azure Functions are wrapped with `FunctionWrapper` that:
- Extracts and validates JWT tokens
- Extracts UPN (User Principal Name) from JWT
- Provides consistent error handling and logging
- Logs detailed request/response information
- Automatically handles exceptions and returns appropriate HTTP status codes

### Error Response Format
All errors return a consistent format:
```json
{
  "result": "KO",
  "errorCode": "400|401|404|500",
  "message": "Error description",
  "data": null
}
```

### Usage in Functions
```csharp
return await _wrapper.Execute(req, async (string jwt, string upn) =>
{
    // Your function logic here
    var result = await _service.DoSomething();
    return new OkObjectResult(new ReturnMessage<T>("OK", "Success", "", result));
});
```

## Swagger Documentation
When `SWAGGER_ENABLED` is set to `true`, Swagger UI will be available at the function app's base URL with `/api/swagger/ui` endpoint.
