using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Entities.Session;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using AgpBeApi.Azure.Functions.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Net;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Functions
{
    public class SourcesTextFunctions
    {
        private readonly ISourcesTextService _sourcesTextService;
        private readonly FunctionWrapper _wrapper;

        public SourcesTextFunctions(ISourcesTextService sourcesTextService, FunctionWrapper wrapper)
        {
            _sourcesTextService = sourcesTextService;
            _wrapper = wrapper;
        }

        [FunctionName("PostSourcesText")]
        [OpenApiOperation(operationId: "PostSourcesText", tags: new[] { "SourcesText" }, Summary = "Create a new sources text record")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(CreateSourcesTextRequest), Description = "Sources text data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Created, contentType: "application/json", bodyType: typeof(ReturnMessage<SourcesTextResponse>), Description = "Created sources text")]
        public async Task<IActionResult> PostSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "api/v1/sources/text")] HttpRequest req,
            ILogger log)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                // Extract user info from JWT
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Create sources text requested by user: {jwtPayload.Email}");

                var createDto = await req.GetBody<CreateSourcesTextRequest>();
                var result = await _sourcesTextService.CreateAsync(createDto);
                
                return new CreatedResult($"/api/v1/sources/text/{result.IdPk}", 
                    new ReturnMessage<SourcesTextResponse>(true, "Sources text created successfully.", "200", result));
            });
        }

        [FunctionName("GetSourcesTextById")]
        [OpenApiOperation(operationId: "GetSourcesTextById", tags: new[] { "SourcesText" }, Summary = "Get sources text by ID")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(int), Description = "Sources text ID")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<SourcesTextResponse>), Description = "Sources text")]
        public async Task<IActionResult> GetSourcesTextById(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "api/v1/sources/text/{id:int}")] HttpRequest req,
            int id,
            ILogger log)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                // Extract user info from JWT
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Get sources text by ID {id} requested by user: {jwtPayload.Email}");

                var result = await _sourcesTextService.GetByIdAsync(id);
                if (result == null)
                {
                    return new NotFoundObjectResult(new ReturnMessage<object>(false, "Sources text not found.", "404", null));
                }
                
                return new OkObjectResult(new ReturnMessage<SourcesTextResponse>(true, "Sources text retrieved successfully.", "200", result));
            });
        }

        [FunctionName("GetSourcesTextByUser")]
        [OpenApiOperation(operationId: "GetSourcesTextByUser", tags: new[] { "SourcesText" }, Summary = "Get sources text by user")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiParameter(name: "userId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Description = "User ID")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<SourcesTextResponse[]>), Description = "Sources text list")]
        public async Task<IActionResult> GetSourcesTextByUser(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "api/v1/sources/text/user/{userId}")] HttpRequest req,
            string userId,
            ILogger log)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                // Extract user info from JWT
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Get sources text by user {userId} requested by user: {jwtPayload.Email}");

                var results = await _sourcesTextService.GetByUserAsync(userId);
                
                return new OkObjectResult(new ReturnMessage<SourcesTextResponse[]>(true, "Sources text retrieved successfully.", "200", results.ToArray()));
            });
        }

        [FunctionName("PutSourcesText")]
        [OpenApiOperation(operationId: "PutSourcesText", tags: new[] { "SourcesText" }, Summary = "Update sources text")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(int), Description = "Sources text ID")]
        [OpenApiRequestBody("application/json", typeof(UpdateSourcesTextRequest), Description = "Sources text update data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<SourcesTextResponse>), Description = "Updated sources text")]
        public async Task<IActionResult> PutSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "api/v1/sources/text/{id:int}")] HttpRequest req,
            int id,
            ILogger log)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                // Extract user info from JWT
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Update sources text {id} requested by user: {jwtPayload.Email}");

                var updateDto = await req.GetBody<UpdateSourcesTextRequest>();
                var result = await _sourcesTextService.UpdateAsync(id, updateDto);
                
                if (result == null)
                {
                    return new NotFoundObjectResult(new ReturnMessage<object>(false, "Sources text not found.", "404", null));
                }
                
                return new OkObjectResult(new ReturnMessage<SourcesTextResponse>(true, "Sources text updated successfully.", "200", result));
            });
        }

        [FunctionName("DeleteSourcesText")]
        [OpenApiOperation(operationId: "DeleteSourcesText", tags: new[] { "SourcesText" }, Summary = "Delete sources text")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(int), Description = "Sources text ID")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Deletion confirmation")]
        public async Task<IActionResult> DeleteSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "api/v1/sources/text/{id:int}")] HttpRequest req,
            int id,
            ILogger log)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                // Extract user info from JWT
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Delete sources text {id} requested by user: {jwtPayload.Email}");

                var success = await _sourcesTextService.DeleteAsync(id);
                
                if (!success)
                {
                    return new NotFoundObjectResult(new ReturnMessage<object>(false, "Sources text not found.", "404", null));
                }
                
                return new OkObjectResult(new ReturnMessage<object>(true, "Sources text deleted successfully.", "200", new { Id = id, Deleted = true }));
            });
        }
    }
}
