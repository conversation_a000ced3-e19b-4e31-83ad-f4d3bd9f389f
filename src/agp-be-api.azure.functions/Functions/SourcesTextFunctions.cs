using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using AgpBeApi.Azure.Functions.Helpers;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System.Net;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Functions
{
    public class SourcesTextFunctions
    {
        private readonly ISourcesTextService _sourcesTextService;
        private readonly FunctionWrapper _wrapper;

        public SourcesTextFunctions(ISourcesTextService sourcesTextService, FunctionWrapper wrapper)
        {
            _sourcesTextService = sourcesTextService;
            _wrapper = wrapper;
        }

        [FunctionName("PostSourcesText")]
        [OpenApiOperation(operationId: "PostSourcesText", tags: ["SourcesText"], Summary = "Create multiple sources text records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(CreateSourcesTextBatchRequest), Description = "Sources text data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Created, contentType: "application/json", bodyType: typeof(ReturnMessage<CreateSourcesTextBatchResponse>), Description = "Creation result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PostSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "api/v1/{idOrganization}/sources/text")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch create sources text requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<CreateSourcesTextBatchRequest>();
                var result = await _sourcesTextService.CreateAsync(batchRequest, jwtPayload.UserId, idOrganization);

                // Return 201 if any items were successful, 400 if all failed
                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.Created : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Created sources_text records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items creation failed";

                return new ObjectResult(new ReturnMessage<CreateSourcesTextBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("PutSourcesTextBatch")]
        [OpenApiOperation(operationId: "PutSourcesText", tags: ["SourcesText"], Summary = "Update multiple sources text records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(UpdateSourcesTextBatchRequest), Description = "Sources text update data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<UpdateSourcesTextBatchResponse>), Description = "Update result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PutSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "api/v1/{idOrganization}/sources/text")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch update sources text requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<UpdateSourcesTextBatchRequest>();
                var result = await _sourcesTextService.UpdateBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Updated sources_text records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items update failed";

                return new ObjectResult(new ReturnMessage<UpdateSourcesTextBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("DeleteSourcesText")]
        [OpenApiOperation(operationId: "DeleteSourcesText", tags: ["SourcesText"], Summary = "Delete multiple sources text records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(DeleteSourcesTextBatchRequest), Description = "Sources text IDs to delete")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<DeleteSourcesTextBatchResponse>), Description = "Delete result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> DeleteSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "api/v1/{idOrganization}/sources/text")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch delete sources text requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<DeleteSourcesTextBatchRequest>();
                var result = await _sourcesTextService.DeleteBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Deleted sources_text records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items delete failed";

                return new ObjectResult(new ReturnMessage<DeleteSourcesTextBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("GetSourcesText")]
        [OpenApiOperation(operationId: "GetSourcesText", tags: ["SourcesText"], Summary = "Get all sources text records for organization")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<List<SourcesTextResponse>>), Description = "Sources text list")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> GetSourcesText(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "api/v1/{idOrganization}/sources/text")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Get sources text requested by user: {jwtPayload.Email} for organization: {idOrganization}");

                var result = await _sourcesTextService.GetByOrganizationAsync(idOrganization);

                return new OkObjectResult(new ReturnMessage<List<SourcesTextResponse>>(
                    true,
                    $"Retrieved {result.Count} sources_text records",
                    "200",
                    result));
            });
        }
    }
}
