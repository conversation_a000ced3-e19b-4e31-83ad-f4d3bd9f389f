using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Net;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Functions
{
    public class HealthFunctions
    {
        private readonly IHealthService _healthService;
        private readonly FunctionWrapper _wrapper;

        public HealthFunctions(IHealthService healthService, FunctionWrapper wrapper)
        {
            _healthService = healthService;
            _wrapper = wrapper;
        }

        [FunctionName("GetHealth")]
        [OpenApiOperation(operationId: "GetHealth", tags: new[] { "Health" }, Summary = "Get API health status with database connections check")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Health status")]
        public async Task<IActionResult> GetHealth(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "api/v1/health")] HttpRequest req,
            ILogger log)
        {
            try
            {
                log.LogInformation("Health check requested");

                var healthStatus = await _healthService.GetHealthStatusAsync();

                return new OkObjectResult(new ReturnMessage<object>(true, "Health check completed", "200", healthStatus));
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error during health check");
                return new ObjectResult(new ReturnMessage<object>(false, "Health check failed", "500", null))
                {
                    StatusCode = StatusCodes.Status500InternalServerError
                };
            }
        }

        [FunctionName("GetReady")]
        [OpenApiOperation(operationId: "GetReady", tags: new[] { "Health" }, Summary = "Simple readiness check")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Ready status")]
        public Task<IActionResult> GetReady(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "api/v1/ready")] HttpRequest req,
            ILogger log)
        {
            log.LogInformation("Ready check requested");

            var readyStatus = new
            {
                Status = "OK",
                Timestamp = DateTime.UtcNow,
                Message = "Service is ready"
            };

            return Task.FromResult<IActionResult>(new OkObjectResult(new ReturnMessage<object>(true, "Ready check completed", "200", readyStatus)));
        }
    }
}
