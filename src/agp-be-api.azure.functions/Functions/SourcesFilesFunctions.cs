using AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO;
using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using AgpBeApi.Azure.Functions.Helpers;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System.Net;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Functions
{
    public class SourcesFilesFunctions
    {
        private readonly ISourcesFilesService _sourcesFilesService;
        private readonly FunctionWrapper _wrapper;

        public SourcesFilesFunctions(ISourcesFilesService sourcesFilesService, FunctionWrapper wrapper)
        {
            _sourcesFilesService = sourcesFilesService;
            _wrapper = wrapper;
        }

        [FunctionName("PostSourcesFiles")]
        [OpenApiOperation(operationId: "PostSourcesFiles", tags: ["SourcesFiles"], Summary = "Create multiple sources files records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(CreateSourcesFilesBatchRequest), Description = "Sources files data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Created, contentType: "application/json", bodyType: typeof(ReturnMessage<CreateSourcesFilesBatchResponse>), Description = "Creation result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PostSourcesFiles(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "api/v1/{idOrganization}/sources/files")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch create sources files requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<CreateSourcesFilesBatchRequest>();
                var result = await _sourcesFilesService.CreateAsync(batchRequest, jwtPayload.UserId, idOrganization);

                // Return 201 if any items were successful, 400 if all failed
                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.Created : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Created sources_files records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items creation failed";

                return new ObjectResult(new ReturnMessage<CreateSourcesFilesBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("PutSourcesFiles")]
        [OpenApiOperation(operationId: "PutSourcesFiles", tags: ["SourcesFiles"], Summary = "Update multiple sources files records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(UpdateSourcesFilesBatchRequest), Description = "Sources files update data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<UpdateSourcesFilesBatchResponse>), Description = "Update result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PutSourcesFiles(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "api/v1/{idOrganization}/sources/files")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch update sources files requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<UpdateSourcesFilesBatchRequest>();
                var result = await _sourcesFilesService.UpdateBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Updated sources_files records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items update failed";

                return new ObjectResult(new ReturnMessage<UpdateSourcesFilesBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("DeleteSourcesFiles")]
        [OpenApiOperation(operationId: "DeleteSourcesFiles", tags: ["SourcesFiles"], Summary = "Delete multiple sources files records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(DeleteSourcesFilesBatchRequest), Description = "Sources files IDs to delete")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<DeleteSourcesFilesBatchResponse>), Description = "Delete result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> DeleteSourcesFiles(
            [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "api/v1/{idOrganization}/sources/files")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch delete sources files requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<DeleteSourcesFilesBatchRequest>();
                var result = await _sourcesFilesService.DeleteBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Deleted sources_files records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items delete failed";

                return new ObjectResult(new ReturnMessage<DeleteSourcesFilesBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("GetSourcesFiles")]
        [OpenApiOperation(operationId: "GetSourcesFiles", tags: ["SourcesFiles"], Summary = "Get all sources files records for organization")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<List<SourcesFilesResponse>>), Description = "Sources files list")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> GetSourcesFiles(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "api/v1/{idOrganization}/sources/files")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Get sources files requested by user: {jwtPayload.Email} for organization: {idOrganization}");

                var result = await _sourcesFilesService.GetByOrganizationAsync(idOrganization);

                return new OkObjectResult(new ReturnMessage<List<SourcesFilesResponse>>(
                    true,
                    $"Retrieved {result.Count} sources_files records",
                    "200",
                    result));
            });
        }
    }
}
