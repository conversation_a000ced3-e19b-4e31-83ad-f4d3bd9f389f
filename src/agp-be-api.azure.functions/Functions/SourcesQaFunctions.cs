using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO;
using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using AgpBeApi.Azure.Functions.Helpers;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System.Net;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Functions
{
    public class SourcesQaFunctions
    {
        private readonly ISourcesQaService _sourcesQaService;
        private readonly FunctionWrapper _wrapper;

        public SourcesQaFunctions(ISourcesQaService sourcesQaService, FunctionWrapper wrapper)
        {
            _sourcesQaService = sourcesQaService;
            _wrapper = wrapper;
        }

        [FunctionName("PostSourcesQa")]
        [OpenApiOperation(operationId: "PostSourcesQa", tags: ["SourcesQa"], Summary = "Create multiple sources QA records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(CreateSourcesQaBatchRequest), Description = "Sources QA data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Created, contentType: "application/json", bodyType: typeof(ReturnMessage<CreateSourcesQaBatchResponse>), Description = "Creation result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PostSourcesQa(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "api/v1/{idOrganization}/sources/qa")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch create sources QA requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<CreateSourcesQaBatchRequest>();
                var result = await _sourcesQaService.CreateAsync(batchRequest, jwtPayload.UserId, idOrganization);

                // Return 201 if any items were successful, 400 if all failed
                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.Created : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Created sources_qa records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items creation failed";

                return new ObjectResult(new ReturnMessage<CreateSourcesQaBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("PutSourcesQa")]
        [OpenApiOperation(operationId: "PutSourcesQa", tags: ["SourcesQa"], Summary = "Update multiple sources QA records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(UpdateSourcesQaBatchRequest), Description = "Sources QA update data")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<UpdateSourcesQaBatchResponse>), Description = "Update result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> PutSourcesQa(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "api/v1/{idOrganization}/sources/qa")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch update sources QA requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<UpdateSourcesQaBatchRequest>();
                var result = await _sourcesQaService.UpdateBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Updated sources_qa records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items update failed";

                return new ObjectResult(new ReturnMessage<UpdateSourcesQaBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("DeleteSourcesQa")]
        [OpenApiOperation(operationId: "DeleteSourcesQa", tags: ["SourcesQa"], Summary = "Delete multiple sources QA records")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody("application/json", typeof(DeleteSourcesQaBatchRequest), Description = "Sources QA IDs to delete")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<DeleteSourcesQaBatchResponse>), Description = "Delete result")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.BadRequest, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Bad request")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> DeleteSourcesQa(
            [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "api/v1/{idOrganization}/sources/qa")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Batch delete sources QA requested by user: {jwtPayload.Email}");

                var batchRequest = await req.GetBody<DeleteSourcesQaBatchRequest>();
                var result = await _sourcesQaService.DeleteBatchAsync(batchRequest, jwtPayload.UserId, idOrganization);

                var statusCode = result.SuccessCount > 0 ? HttpStatusCode.OK : HttpStatusCode.BadRequest;
                var message = result.SuccessCount > 0
                    ? $"Deleted sources_qa records: {result.SuccessCount} successful, {result.FailureCount} failed"
                    : "All items delete failed";

                return new ObjectResult(new ReturnMessage<DeleteSourcesQaBatchResponse>(
                    result.SuccessCount > 0,
                    message,
                    ((int)statusCode).ToString(),
                    result))
                {
                    StatusCode = (int)statusCode
                };
            });
        }

        [FunctionName("GetSourcesQa")]
        [OpenApiOperation(operationId: "GetSourcesQa", tags: ["SourcesQa"], Summary = "Get all sources QA records for organization")]
        [OpenApiSecurity("bearer_auth", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ReturnMessage<List<SourcesQaResponse>>), Description = "Sources QA list")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Unauthorized, contentType: "application/json", bodyType: typeof(ReturnMessage<object>), Description = "Unauthorized")]
        public async Task<IActionResult> GetSourcesQa(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "api/v1/{idOrganization}/sources/qa")] HttpRequest req,
            ILogger log, string idOrganization)
        {
            return await _wrapper.Execute(req, async (string jwt) =>
            {
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, log);
                log.LogInformation($"Get sources QA requested by user: {jwtPayload.Email} for organization: {idOrganization}");

                var result = await _sourcesQaService.GetByOrganizationAsync(idOrganization);

                return new OkObjectResult(new ReturnMessage<List<SourcesQaResponse>>(
                    true,
                    $"Retrieved {result.Count} sources_qa records",
                    "200",
                    result));
            });
        }
    }
}
