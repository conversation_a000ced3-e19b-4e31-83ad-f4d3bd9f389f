<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <_FunctionsSkipCleanOutput>true</_FunctionsSkipCleanOutput>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591;NU1608</NoWarn>
  </PropertyGroup>
  
  <ItemGroup>
    <!-- Azure Functions Core (In-Process) -->
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />

    <!-- Azure Identity and KeyVault (updated versions to fix vulnerabilities) -->
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />

    <!-- Entity Framework PostgreSQL -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />

    <!-- Swagger/OpenAPI -->
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.OpenApi" Version="1.5.1" />

    <!-- Fix for CodeAnalysis conflicts -->
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.5.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.5.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.5.0" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />

    <!-- Application Insights -->
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.22.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />

    <!-- Redis (if needed) -->
    <PackageReference Include="StackExchange.Redis" Version="2.8.0" />

    <!-- Configuration -->
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />

    <!-- JSON Serialization -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- JWT Token Handling (updated to fix vulnerability) -->
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
  </ItemGroup>
  
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>

  </ItemGroup>
</Project>
