using System;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using AgpBeApi.Azure.Functions.Entities;
using AgpBeApi.Azure.Functions.Entities.Session;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.Helpers;
using AgpBeApi.Azure.Functions.Exceptions;
using AgpBeApi.Azure.Functions.Entities.ErrorMessages;

namespace AgpBeApi.Azure.Functions.FunctionsUtilities
{
    /// <summary>
    /// This class is used inside functions to validate JWT tokens
    /// using an external authentication service.
    /// </summary>
    public class FunctionWrapper
    {
        private readonly ILogger _log;
        private readonly ITokenValidationService _tokenValidationService;

        public delegate Task<IActionResult> Func<out TResult>(string jwt);

        public FunctionWrapper(ILogger<FunctionWrapper> log, ITokenValidationService tokenValidationService)
        {
            _log = log;
            _tokenValidationService = tokenValidationService;
        }

        /// <summary>
        /// This function validates JWT token using external auth service.
        /// </summary>
        /// <param name="req"> request header </param>
        /// <param name="azureFunction"> azure function </param>
        /// <returns>
        /// The error if token is invalid, otherwise continue with the function execution.
        /// </returns>
        public async Task<IActionResult> Execute(HttpRequest req, Func<string, Task<IActionResult>> azureFunction)
        {
            try
            {
                // Extract Authorization header
                if (!req.Headers.ContainsKey("Authorization"))
                {
                    _log.LogWarning("Missing Authorization header");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        false,
                        "Missing Authorization header.",
                        "403",
                        null));
                }

                var authHeader = req.Headers["Authorization"].ToString();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    _log.LogWarning("Invalid Authorization header format");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        false,
                        "Invalid Authorization header format.",
                        "403",
                        null));
                }

                // Extract JWT token
                var jwt = authHeader.Replace("Bearer ", "").Trim();
                if (string.IsNullOrEmpty(jwt))
                {
                    _log.LogWarning("Empty JWT token");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        false,
                        "Empty JWT token forbidden.",
                        "403",
                        null));
                }

                // Parse JWT payload
                _log.LogInformation("Parsing JWT payload");
                var jwtPayload = JwtTokenHelper.ParseJwtPayload(jwt, _log);

                if (jwtPayload == null)
                {
                    _log.LogWarning("Failed to parse JWT payload");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        false,
                        "Invalid jwt parsing",
                        "401",
                        null));
                }

                // Check session in Redis and get session data
                _log.LogInformation($"Checking session in Redis: {jwtPayload.SessionId}");
                var redisClient = new RedisClient(_log);
                var sessionData = await redisClient.GetSessionAsync(jwtPayload.SessionId);

                if (sessionData == null)
                {
                    _log.LogWarning($"Session not found in Redis: {jwtPayload.SessionId}");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        false,
                        "Session not found redis",
                        "401",
                        null));
                }

                // Check user permissions based on HTTP method
                var httpMethod = req.Method;
                var hasPermission = RoleAuthorizationHelper.HasPermission(jwtPayload, sessionData, httpMethod, _log);

                if (!hasPermission)
                {
                    var requiredPermission = RoleAuthorizationHelper.GetRequiredPermissionDescription(httpMethod);
                    _log.LogWarning($"User {jwtPayload.Email} lacks {requiredPermission} for {httpMethod} request");
                    return new ObjectResult(new ReturnMessage<object>(
                        false,
                        $"Insufficient permissions. Required: {requiredPermission}",
                        "403",
                        null))
                    {
                        StatusCode = StatusCodes.Status403Forbidden
                    };
                }

                // Validate JWT token with Gateway
                _log.LogInformation("Validating JWT token with Gateway");
                var validationResult = await _tokenValidationService.ValidateTokenAsync(jwt);

                if (!validationResult.Status)
                {
                    _log.LogWarning($"Gateway token validation failed: {validationResult.Message}");
                    return new UnauthorizedObjectResult(new ReturnMessage<object>(
                        validationResult.Status,
                        validationResult.Message,
                        validationResult.Code,
                        validationResult.Data));
                }

                _log.LogInformation("Token validation successful");

                // Execute the function with validated token
                var result = await azureFunction(jwt);

                return result;
            }
            catch (AuthHeaderNotFoundException e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                    UnauthorizedRequest("Jwt token not found: " + msg));
                errorObjectResult.StatusCode = StatusCodes.Status401Unauthorized;
                return errorObjectResult;
            }
            catch (NullReferenceException e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        BadRequest("Bad request: " + msg + " Probably missing values in the body or header."));
                errorObjectResult.StatusCode = StatusCodes.Status400BadRequest;
                return errorObjectResult;
            }
            catch (BadRequestException e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        BadRequest(e.Message));
                errorObjectResult.StatusCode = StatusCodes.Status400BadRequest;
                return errorObjectResult;
            }
            catch (InternalServerErrorException e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        InternalServerErrorResponse(e.Message));
                errorObjectResult.StatusCode = StatusCodes.Status500InternalServerError;
                return errorObjectResult;
            }
            catch (NotFoundException e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        NotFound(e.Message));
                errorObjectResult.StatusCode = StatusCodes.Status404NotFound;
                return errorObjectResult;
            }
            catch (Newtonsoft.Json.JsonSerializationException e)
            {
                string msg = e.InnerException == null
                    ? e.Message
                    : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        BadRequest(e.Message));
                errorObjectResult.StatusCode = StatusCodes.Status400BadRequest;
                return errorObjectResult;
            }
            catch (Exception e)
            {
                string msg = e.InnerException == null
                ? e.Message
                : e.Message + " --> " + e.InnerException;
                _log.LogError(e, msg);
                ObjectResult errorObjectResult = new ObjectResult(new
                        InternalServerErrorResponse("Internal server error " + e.Message));
                errorObjectResult.StatusCode = StatusCodes.Status500InternalServerError;
                return errorObjectResult;
            }
        }
    }
}
