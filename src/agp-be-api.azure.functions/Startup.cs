using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager;
using AgpBeApi.Azure.Functions.Helpers;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.Services;
using AgpBeApi.Azure.Functions.FunctionsUtilities;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using System;
using StackExchange.Redis;

[assembly: FunctionsStartup(typeof(AgpBeApi.Azure.Functions.Startup))]
namespace AgpBeApi.Azure.Functions
{
    class Startup : FunctionsStartup
    {
        public override void Configure(IFunctionsHostBuilder builder)
        {
            // Get configuration from environment variables
            IConfiguration config = builder.GetContext().Configuration;

            // Configure PostgreSQL DbContext
            var connectionString = ConfigHelper.GetPostgreSqlConnectionString();
            builder.Services.AddDbContext<AgpDbSupportContext>(options =>
                options.UseNpgsql(connectionString));

            // Configure Redis if connection string is provided
            var redisConnectionString = ConfigHelper.RedisConnectionString;
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                try
                {
                    builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
                        ConnectionMultiplexer.Connect(redisConnectionString));
                }
                catch (Exception ex)
                {
                    // Log Redis connection error but don't fail startup
                    System.Diagnostics.Debug.WriteLine($"Redis connection failed: {ex.Message}");
                }
            }
            else
            {
                // Register a null Redis connection for optional dependency
                builder.Services.AddSingleton<IConnectionMultiplexer>(provider => null);
            }

            // Register Services
            builder.Services.AddScoped<ISourcesTextService, SourcesTextService>();
            builder.Services.AddScoped<IHealthService, HealthService>();
            builder.Services.AddScoped<ITokenValidationService, TokenValidationService>();

            // Register DB Support Managers
            builder.Services.AddScoped<ISourcesTextManager, SourcesTextManager>();

            // Adding the wrapper to the builder
            builder.Services.AddTransient<FunctionWrapper>();

            // HTTP Client Factory
            builder.Services.AddHttpClient();

            // Perform startup health checks
            PerformStartupHealthChecks();
        }

        public override void ConfigureAppConfiguration(IFunctionsConfigurationBuilder builder)
        {
            // Use environment variables configuration
            builder.ConfigurationBuilder
                .SetBasePath(Environment.CurrentDirectory)
                .AddEnvironmentVariables()
                .Build();
        }

        /// <summary>
        /// Performs health checks for critical services during application startup
        /// </summary>
        private static void PerformStartupHealthChecks()
        {
            Console.WriteLine("🔍 Starting health checks...");

            var postgresHealthy = CheckPostgreSqlConnection();
            var redisHealthy = CheckRedisConnection();

            Console.WriteLine($"📊 Health Check Results:");
            Console.WriteLine($"*  PostgreSQL: {(postgresHealthy ? "✅ Healthy" : "❌ Unhealthy")}");
            Console.WriteLine($"*  Redis: {(redisHealthy ? "✅ Healthy" : "❌ Unhealthy")}");

            if (!postgresHealthy)
            {
                Console.WriteLine("⚠️  WARNING: PostgreSQL connection failed. Database operations will not work.");
            }

            if (!redisHealthy)
            {
                Console.WriteLine("⚠️  WARNING: Redis connection failed. Session management will not work.");
            }

            if (postgresHealthy && redisHealthy)
            {
                Console.WriteLine("🎉 All services are healthy!");
            }

            Console.WriteLine("🚀 Application startup completed.");
        }

        /// <summary>
        /// Checks PostgreSQL database connection
        /// </summary>
        private static bool CheckPostgreSqlConnection()
        {
            try
            {
                var connectionString = ConfigHelper.GetPostgreSqlConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    Console.WriteLine("❌ PostgreSQL: Connection string not configured");
                    return false;
                }

                using var context = new AgpDbSupportContext(new DbContextOptionsBuilder<AgpDbSupportContext>()
                    .UseNpgsql(connectionString)
                    .Options);

                // Test connection by opening it
                var canConnect = context.Database.CanConnect();

                if (canConnect)
                {
                    Console.WriteLine("✅ PostgreSQL: Connection successful");

                    // Optional: Check if database has expected tables
                    try
                    {
                        var tableExists = context.Database.GetDbConnection().State == System.Data.ConnectionState.Open ||
                                        context.Database.CanConnect();
                        if (tableExists)
                        {
                            Console.WriteLine("✅ PostgreSQL: Database schema accessible");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"⚠️  PostgreSQL: Schema check failed: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine("❌ PostgreSQL: Cannot connect to database");
                }

                return canConnect;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ PostgreSQL: Connection failed - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks Redis connection
        /// </summary>
        private static bool CheckRedisConnection()
        {
            try
            {
                var redisConnectionString = ConfigHelper.GetRedisConnectionString();
                if (string.IsNullOrEmpty(redisConnectionString))
                {
                    Console.WriteLine("❌ Redis: Connection string not configured");
                    return false;
                }

                using var connection = ConnectionMultiplexer.Connect(redisConnectionString);
                var database = connection.GetDatabase();

                // Test Redis with a simple ping
                var pingResult = database.Ping();

                if (pingResult.TotalMilliseconds > 0)
                {
                    Console.WriteLine($"✅ Redis: Connection successful (ping: {pingResult.TotalMilliseconds:F2}ms)");

                    // Test basic operations
                    var testKey = "startup_health_check";
                    var testValue = DateTime.UtcNow.ToString();

                    database.StringSet(testKey, testValue, TimeSpan.FromSeconds(10));
                    var retrievedValue = database.StringGet(testKey);

                    if (retrievedValue == testValue)
                    {
                        Console.WriteLine("✅ Redis: Read/Write operations working");
                        database.KeyDelete(testKey); // Cleanup
                    }
                    else
                    {
                        Console.WriteLine("⚠️  Redis: Read/Write test failed");
                    }
                }
                else
                {
                    Console.WriteLine("❌ Redis: Ping failed");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Redis: Connection failed - {ex.Message}");
                return false;
            }
        }
    }
}
