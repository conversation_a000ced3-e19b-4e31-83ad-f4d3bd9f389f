using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SourcesTextService : ISourcesTextService
    {
        private readonly ISourcesTextManager _sourcesTextManager;
        private readonly ILogger<SourcesTextService> _logger;

        public SourcesTextService(ISourcesTextManager sourcesTextManager, ILogger<SourcesTextService> logger)
        {
            _sourcesTextManager = sourcesTextManager;
            _logger = logger;
        }

        public async Task<SourcesTextResponse> CreateAsync(CreateSourcesTextRequest createRequest)
        {
            try
            {
                if (createRequest == null)
                {
                    throw new BadRequestException("CreateSourcesTextRequest cannot be null");
                }

                if (string.IsNullOrEmpty(createRequest.IdUserFk))
                {
                    throw new BadRequestException("IdUserFk is required");
                }

                if (string.IsNullOrEmpty(createRequest.IdOrganizationFk))
                {
                    throw new BadRequestException("IdOrganizationFk is required");
                }

                var sourcesTextEntity = new SourcesTextEntity
                {
                    IdUserFk = createRequest.IdUserFk,
                    IdOrganizationFk = createRequest.IdOrganizationFk,
                    Timestamp = createRequest.Timestamp,
                    Title = createRequest.Title,
                    Text = createRequest.Text
                };

                var createdEntity = await _sourcesTextManager.CreateSourcesTextAsync(sourcesTextEntity);
                return MapToResponse(createdEntity);
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSourcesTextAsync");
                throw new InternalServerErrorException("Failed to create sources text record", ex);
            }
        }

        public async Task<SourcesTextResponse> GetByIdAsync(int id)
        {
            try
            {
                if (id <= 0)
                {
                    throw new BadRequestException("ID must be greater than 0");
                }

                var entity = await _sourcesTextManager.GetSourcesTextByIdAsync(id);

                if (entity == null)
                {
                    throw new NotFoundException($"Sources text record with ID {id} not found");
                }

                return MapToResponse(entity);
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (NotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByIdAsync for ID: {id}");
                throw new InternalServerErrorException($"Failed to retrieve sources text record with ID {id}", ex);
            }
        }

        public async Task<List<SourcesTextResponse>> GetByUserAsync(string userId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByUserIdAsync(userId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByUserIdAsync for user: {userId}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetByOrganizationAsync(string organizationId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByOrganizationIdAsync(organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByOrganizationIdAsync for organization: {organizationId}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetByUserAndOrganizationAsync(string userId, string organizationId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByUserAndOrganizationAsync(userId, organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByUserAndOrganizationAsync for user: {userId}, organization: {organizationId}");
                throw;
            }
        }

        public async Task<SourcesTextResponse> UpdateAsync(int id, UpdateSourcesTextRequest updateRequest)
        {
            try
            {
                var existingEntity = await _sourcesTextManager.GetSourcesTextByIdAsync(id);
                if (existingEntity == null)
                {
                    return null;
                }

                existingEntity.Timestamp = updateRequest.Timestamp;
                existingEntity.Title = updateRequest.Title;
                existingEntity.Text = updateRequest.Text;

                var updatedEntity = await _sourcesTextManager.UpdateSourcesTextAsync(existingEntity);
                return updatedEntity != null ? MapToResponse(updatedEntity) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateSourcesTextAsync for ID: {id}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                return await _sourcesTextManager.DeleteSourcesTextAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in DeleteSourcesTextAsync for ID: {id}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetAllAsync()
        {
            try
            {
                var entities = await _sourcesTextManager.GetAllSourcesTextAsync();
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllSourcesTextAsync");
                throw;
            }
        }

        private static SourcesTextResponse MapToResponse(SourcesTextEntity entity)
        {
            return new SourcesTextResponse
            {
                IdPk = entity.IdPk,
                IdUserFk = entity.IdUserFk,
                IdOrganizationFk = entity.IdOrganizationFk,
                Timestamp = entity.Timestamp,
                Title = entity.Title,
                Text = entity.Text,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }
    }
}
