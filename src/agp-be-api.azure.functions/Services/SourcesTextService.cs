using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using AgpBeApi.Azure.Functions.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SourcesTextService : ISourcesTextService
    {
        private readonly ISourcesTextManager _sourcesTextManager;
        private readonly ILogger<SourcesTextService> _logger;

        public SourcesTextService(ISourcesTextManager sourcesTextManager, ILogger<SourcesTextService> logger)
        {
            _sourcesTextManager = sourcesTextManager;
            _logger = logger;
        }

        public async Task<CreateSourcesTextBatchResponse> CreateAsync(
            CreateSourcesTextBatchRequest batchRequest,
            string idUser,
            string idOrganization
        )
        {
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(idUser))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new CreateSourcesTextBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                var entities = new List<SourcesTextEntity>();

                // Validate all items first
                for (int i = 0; i < batchRequest.Items.Count; i++)
                {
                    var item = batchRequest.Items[i];
                    try
                    {
                        // Validate individual item
                        if (item == null)
                        {
                            response.FailedItems.Add(new BatchErrorItem
                            {
                                Index = i,
                                Error = "Item cannot be null",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate required fields from item
                        if (string.IsNullOrEmpty(item.Title))
                        {
                            response.FailedItems.Add(new BatchErrorItem
                            {
                                Index = i,
                                Error = "Title is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (string.IsNullOrEmpty(item.Text))
                        {
                            response.FailedItems.Add(new BatchErrorItem
                            {
                                Index = i,
                                Error = "Text is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Create entity using parameters passed to method
                        var entity = new SourcesTextEntity
                        {
                            IdUserFk = idUser,                    // Use parameter
                            IdOrganizationFk = idOrganization,    // Use parameter
                            Timestamp = DateTime.UtcNow,
                            Title = item.Title,
                            Text = item.Text
                        };

                        entities.Add(entity);
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchErrorItem
                        {
                            Index = i,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                // Insert valid entities in batch
                if (entities.Any())
                {
                    try
                    {
                        var createdEntities = await _sourcesTextManager.CreateSourcesTextAsync(
                            entities,
                            idUser,
                            idOrganization
                        );
                        response.SuccessfulItems = createdEntities.Select(MapToResponse).ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in batch insert operation");

                        // If batch insert fails, mark all as failed
                        for (int i = 0; i < entities.Count; i++)
                        {
                            var originalIndex = i; // This would need better tracking in real implementation
                            response.FailedItems.Add(new BatchErrorItem
                            {
                                Index = originalIndex,
                                Error = $"Batch insert failed: {ex.Message}",
                                OriginalItem = batchRequest.Items[originalIndex]
                            });
                        }
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                _logger.LogInformation($"Batch create completed: {response.SuccessCount} successful, {response.FailureCount} failed");

                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSourcesTextBatchAsync");
                throw new InternalServerErrorException("Failed to create sources text batch", ex);
            }
        }

        public async Task<SourcesTextResponse> GetByIdAsync(int id)
        {
            try
            {
                if (id <= 0)
                {
                    throw new BadRequestException("ID must be greater than 0");
                }

                var entity = await _sourcesTextManager.GetSourcesTextByIdAsync(id);

                if (entity == null)
                {
                    throw new NotFoundException($"Sources text record with ID {id} not found");
                }

                return MapToResponse(entity);
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (NotFoundException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByIdAsync for ID: {id}");
                throw new InternalServerErrorException($"Failed to retrieve sources text record with ID {id}", ex);
            }
        }

        public async Task<List<SourcesTextResponse>> GetByUserAsync(string userId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByUserIdAsync(userId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByUserIdAsync for user: {userId}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetByOrganizationAsync(string organizationId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByOrganizationIdAsync(organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByOrganizationIdAsync for organization: {organizationId}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetByUserAndOrganizationAsync(string userId, string organizationId)
        {
            try
            {
                var entities = await _sourcesTextManager.GetSourcesTextByUserAndOrganizationAsync(userId, organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetSourcesTextByUserAndOrganizationAsync for user: {userId}, organization: {organizationId}");
                throw;
            }
        }

        public async Task<SourcesTextResponse> UpdateAsync(int id, UpdateSourcesTextRequest updateRequest)
        {
            try
            {
                var existingEntity = await _sourcesTextManager.GetSourcesTextByIdAsync(id);
                if (existingEntity == null)
                {
                    return null;
                }

                existingEntity.Timestamp = updateRequest.Timestamp;
                existingEntity.Title = updateRequest.Title;
                existingEntity.Text = updateRequest.Text;

                var updatedEntity = await _sourcesTextManager.UpdateSourcesTextAsync(existingEntity);
                return updatedEntity != null ? MapToResponse(updatedEntity) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateSourcesTextAsync for ID: {id}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                return await _sourcesTextManager.DeleteSourcesTextAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in DeleteSourcesTextAsync for ID: {id}");
                throw;
            }
        }

        public async Task<List<SourcesTextResponse>> GetAllAsync()
        {
            try
            {
                var entities = await _sourcesTextManager.GetAllSourcesTextAsync();
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllSourcesTextAsync");
                throw;
            }
        }

        private static SourcesTextResponse MapToResponse(SourcesTextEntity entity)
        {
            return new SourcesTextResponse
            {
                IdPk = entity.IdPk,
                IdUserFk = entity.IdUserFk,
                IdOrganizationFk = entity.IdOrganizationFk,
                Timestamp = entity.Timestamp,
                Title = entity.Title,
                Text = entity.Text,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }
    }
}
