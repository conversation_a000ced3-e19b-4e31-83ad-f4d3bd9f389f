using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO;
using AgpBeApi.Azure.Functions.Exceptions;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SourcesQaService : ISourcesQaService
    {
        private readonly ISourcesQaManager _sourcesQaManager;
        private readonly ISourcesQaQuestionsManager _questionsManager;
        private readonly ILogger<SourcesQaService> _logger;

        public SourcesQaService(
            ISourcesQaManager sourcesQaManager, 
            ISourcesQaQuestionsManager questionsManager,
            ILogger<SourcesQaService> logger)
        {
            _sourcesQaManager = sourcesQaManager;
            _questionsManager = questionsManager;
            _logger = logger;
        }

        public async Task<CreateSourcesQaBatchResponse> CreateAsync(
            CreateSourcesQaBatchRequest batchRequest,
            string userId,
            string idOrganization)
        {
            await Task.Delay(0); // Per async compliance
            
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new CreateSourcesQaBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                var entities = new List<SourcesQaEntity>();

                // Validate all items first
                for (int i = 0; i < batchRequest.Items.Count; i++)
                {
                    var item = batchRequest.Items[i];
                    try
                    {
                        // Validate individual item
                        if (item == null)
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Item cannot be null",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate required fields
                        if (string.IsNullOrEmpty(item.Title))
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Title is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (string.IsNullOrEmpty(item.Answer))
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Answer is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (item.Questions == null || !item.Questions.Any())
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "At least one question is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Create questions first (senza id_user_fk)
                        var now = DateTime.UtcNow;
                        var questionEntities = item.Questions.Select(q => new SourcesQaQuestionsEntity
                        {
                            Text = q.Text,
                            Timestamp = now,
                            CreatedAt = now,
                            UpdatedAt = now
                        }).ToList();

                        // Log per debug
                        _logger.LogInformation($"Creating {questionEntities.Count} questions");

                        var createdQuestions = _questionsManager.CreateQuestions(questionEntities);
                        var questionIds = string.Join(",", createdQuestions.Select(q => q.IdPk));

                        // Create QA entity if validation passes
                        var now = DateTime.UtcNow;
                        var entity = new SourcesQaEntity
                        {
                            IdUserFk = userId,
                            IdOrganizationFk = idOrganization,
                            QuestionsIdsFk = questionIds,
                            Title = item.Title,
                            Answer = item.Answer,
                            Timestamp = now,
                            CreatedAt = now,
                            UpdatedAt = now
                        };

                        entities.Add(entity);
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchQaErrorItem
                        {
                            Index = i,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                // Insert valid entities in batch
                if (entities.Any())
                {
                    try
                    {
                        var createdEntities = _sourcesQaManager.CreateSourcesQa(entities, userId, idOrganization);
                        response.SuccessfulItems = createdEntities.Select(MapToResponse).ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in batch insert operation");

                        // If batch insert fails, mark all as failed
                        for (int i = 0; i < entities.Count; i++)
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = $"Batch insert failed: {ex.Message}",
                                OriginalItem = batchRequest.Items[i]
                            });
                        }
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list of all items for the organization
                try
                {
                    var refreshedItems = _sourcesQaManager.GetSourcesQaByOrganizationId(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                    _logger.LogInformation($"Added {response.RefreshedItems.Count} refreshed QA items to response");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed QA items, continuing without them");
                    response.RefreshedItems = new List<SourcesQaResponse>();
                }

                _logger.LogInformation($"Batch create completed: {response.SuccessCount} successful, {response.FailureCount} failed");

                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSourcesQaBatchAsync");
                throw new InternalServerErrorException("Failed to create sources QA batch", ex);
            }
        }

        private SourcesQaResponse MapToResponse(SourcesQaEntity entity)
        {
            var response = new SourcesQaResponse
            {
                IdPk = entity.IdPk,
                IdUserFk = entity.IdUserFk,
                IdOrganizationFk = entity.IdOrganizationFk,
                Title = entity.Title,
                Answer = entity.Answer,
                Timestamp = entity.Timestamp,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt,
                Questions = new List<SourcesQaQuestionResponse>()
            };

            // Parse question IDs and get questions
            if (!string.IsNullOrEmpty(entity.QuestionsIdsFk))
            {
                try
                {
                    var questionIds = entity.QuestionsIdsFk
                        .Split(',')
                        .Where(id => int.TryParse(id.Trim(), out _))
                        .Select(id => int.Parse(id.Trim()))
                        .ToList();

                    if (questionIds.Any())
                    {
                        var questions = _questionsManager.GetQuestionsByIds(questionIds);
                        response.Questions = questions.Select(q => new SourcesQaQuestionResponse
                        {
                            IdPk = q.IdPk,
                            Text = q.Text,
                            Timestamp = q.Timestamp,
                            CreatedAt = q.CreatedAt,
                            UpdatedAt = q.UpdatedAt
                        }).ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Error parsing question IDs for QA {entity.IdPk}: {entity.QuestionsIdsFk}");
                }
            }

            return response;
        }

        public async Task<UpdateSourcesQaBatchResponse> UpdateBatchAsync(UpdateSourcesQaBatchRequest batchRequest, string userId, string idOrganization)
        {
            await Task.Delay(0); // Per async compliance

            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new UpdateSourcesQaBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                // Process each item
                foreach (var item in batchRequest.Items)
                {
                    try
                    {
                        // Validate item
                        if (item.IdPk <= 0)
                        {
                            response.FailedItems.Add(new BatchQaUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Invalid ID",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Get existing entity
                        var existingEntity = _sourcesQaManager.GetSourcesQaById(item.IdPk);
                        if (existingEntity == null)
                        {
                            response.FailedItems.Add(new BatchQaUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Record not found",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Check ownership
                        if (existingEntity.IdUserFk != userId || existingEntity.IdOrganizationFk != idOrganization)
                        {
                            response.FailedItems.Add(new BatchQaUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Access denied",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Delete old questions and create new ones
                        if (!string.IsNullOrEmpty(existingEntity.QuestionsIdsFk))
                        {
                            var oldQuestionIds = existingEntity.QuestionsIdsFk
                                .Split(',')
                                .Where(id => int.TryParse(id.Trim(), out _))
                                .Select(id => int.Parse(id.Trim()))
                                .ToList();

                            foreach (var oldId in oldQuestionIds)
                            {
                                _questionsManager.DeleteQuestion(oldId);
                            }
                        }

                        // Create new questions (senza id_user_fk)
                        var questionEntities = item.Questions.Select(q => new SourcesQaQuestionsEntity
                        {
                            Text = q.Text,
                            Timestamp = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        }).ToList();

                        var createdQuestions = _questionsManager.CreateQuestions(questionEntities);
                        var questionIds = string.Join(",", createdQuestions.Select(q => q.IdPk));

                        // Update entity
                        existingEntity.QuestionsIdsFk = questionIds;
                        existingEntity.Title = item.Title;
                        existingEntity.Answer = item.Answer;
                        existingEntity.UpdatedAt = DateTime.UtcNow;

                        // Force UTC for existing DateTime fields
                        existingEntity.CreatedAt = DateTime.SpecifyKind(existingEntity.CreatedAt, DateTimeKind.Utc);
                        existingEntity.Timestamp = DateTime.SpecifyKind(existingEntity.Timestamp, DateTimeKind.Utc);

                        var updatedEntity = _sourcesQaManager.UpdateSourcesQa(existingEntity);
                        response.SuccessfulItems.Add(MapToResponse(updatedEntity));
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchQaUpdateErrorItem
                        {
                            IdPk = item.IdPk,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list
                try
                {
                    var refreshedItems = _sourcesQaManager.GetSourcesQaByOrganizationId(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed QA items, continuing without them");
                    response.RefreshedItems = new List<SourcesQaResponse>();
                }

                _logger.LogInformation($"Batch update completed: {response.SuccessCount} successful, {response.FailureCount} failed");
                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateBatchAsync");
                throw new InternalServerErrorException("Failed to update sources QA batch", ex);
            }
        }

        public async Task<DeleteSourcesQaBatchResponse> DeleteBatchAsync(DeleteSourcesQaBatchRequest batchRequest, string userId, string idOrganization)
        {
            await Task.Delay(0); // Per async compliance

            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new DeleteSourcesQaBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                // Process each item
                foreach (var idPk in batchRequest.Items)
                {
                    try
                    {
                        // Validate ID
                        if (idPk <= 0)
                        {
                            response.FailedItems.Add(new BatchQaDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Invalid ID"
                            });
                            continue;
                        }

                        // Get existing entity
                        var existingEntity = _sourcesQaManager.GetSourcesQaById(idPk);
                        if (existingEntity == null)
                        {
                            response.FailedItems.Add(new BatchQaDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Record not found"
                            });
                            continue;
                        }

                        // Check ownership
                        if (existingEntity.IdUserFk != userId || existingEntity.IdOrganizationFk != idOrganization)
                        {
                            response.FailedItems.Add(new BatchQaDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Access denied"
                            });
                            continue;
                        }

                        // Delete associated questions first
                        if (!string.IsNullOrEmpty(existingEntity.QuestionsIdsFk))
                        {
                            var questionIds = existingEntity.QuestionsIdsFk
                                .Split(',')
                                .Where(id => int.TryParse(id.Trim(), out _))
                                .Select(id => int.Parse(id.Trim()))
                                .ToList();

                            foreach (var questionId in questionIds)
                            {
                                _questionsManager.DeleteQuestion(questionId);
                            }
                        }

                        // Delete QA entity
                        var deleted = _sourcesQaManager.DeleteSourcesQa(idPk);
                        if (deleted)
                        {
                            response.SuccessfulItems.Add(idPk);
                        }
                        else
                        {
                            response.FailedItems.Add(new BatchQaDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Delete operation failed"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchQaDeleteErrorItem
                        {
                            IdPk = idPk,
                            Error = ex.Message
                        });
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list
                try
                {
                    var refreshedItems = _sourcesQaManager.GetSourcesQaByOrganizationId(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed QA items, continuing without them");
                    response.RefreshedItems = new List<SourcesQaResponse>();
                }

                _logger.LogInformation($"Batch delete completed: {response.SuccessCount} successful, {response.FailureCount} failed");
                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DeleteBatchAsync");
                throw new InternalServerErrorException("Failed to delete sources QA batch", ex);
            }
        }

        public async Task<List<SourcesQaResponse>> GetByOrganizationAsync(string organizationId)
        {
            await Task.Delay(0); // Per async compliance

            try
            {
                var entities = _sourcesQaManager.GetSourcesQaByOrganizationId(organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSourcesQaByOrganizationAsync");
                throw new InternalServerErrorException("Failed to get sources QA records by organization", ex);
            }
        }
    }
}
