using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO;
using AgpBeApi.Azure.Functions.Exceptions;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SourcesQaService : ISourcesQaService
    {
        private readonly ISourcesQaManager _sourcesQaManager;
        private readonly ISourcesQaQuestionsManager _questionsManager;
        private readonly ILogger<SourcesQaService> _logger;

        public SourcesQaService(
            ISourcesQaManager sourcesQaManager, 
            ISourcesQaQuestionsManager questionsManager,
            ILogger<SourcesQaService> logger)
        {
            _sourcesQaManager = sourcesQaManager;
            _questionsManager = questionsManager;
            _logger = logger;
        }

        public async Task<CreateSourcesQaBatchResponse> CreateAsync(
            CreateSourcesQaBatchRequest batchRequest,
            string idOrganization)
        {
            await Task.Delay(0); // Per async compliance
            
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new CreateSourcesQaBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                var entities = new List<SourcesQaEntity>();

                // Validate all items first
                for (int i = 0; i < batchRequest.Items.Count; i++)
                {
                    var item = batchRequest.Items[i];
                    try
                    {
                        // Validate individual item
                        if (item == null)
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Item cannot be null",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate required fields
                        if (string.IsNullOrEmpty(item.Title))
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Title is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (string.IsNullOrEmpty(item.Answer))
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "Answer is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (item.Questions == null || !item.Questions.Any())
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = "At least one question is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Create questions first
                        var questionEntities = item.Questions.Select(q => new SourcesQaQuestionsEntity
                        {
                            Text = q.Text,
                            Timestamp = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        }).ToList();

                        var createdQuestions = _questionsManager.CreateQuestions(questionEntities);
                        var questionIds = string.Join(",", createdQuestions.Select(q => q.IdPk));

                        // Create QA entity if validation passes
                        var now = DateTime.UtcNow;
                        var entity = new SourcesQaEntity
                        {
                            IdOrganizationFk = idOrganization,
                            QuestionsIdsFk = questionIds,
                            Title = item.Title,
                            Answer = item.Answer,
                            Timestamp = now,
                            CreatedAt = now,
                            UpdatedAt = now
                        };

                        entities.Add(entity);
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchQaErrorItem
                        {
                            Index = i,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                // Insert valid entities in batch
                if (entities.Any())
                {
                    try
                    {
                        var createdEntities = _sourcesQaManager.CreateSourcesQa(entities, idOrganization);
                        response.SuccessfulItems = createdEntities.Select(MapToResponse).ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in batch insert operation");

                        // If batch insert fails, mark all as failed
                        for (int i = 0; i < entities.Count; i++)
                        {
                            response.FailedItems.Add(new BatchQaErrorItem
                            {
                                Index = i,
                                Error = $"Batch insert failed: {ex.Message}",
                                OriginalItem = batchRequest.Items[i]
                            });
                        }
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list of all items for the organization
                try
                {
                    var refreshedItems = _sourcesQaManager.GetSourcesQaByOrganizationId(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                    _logger.LogInformation($"Added {response.RefreshedItems.Count} refreshed QA items to response");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed QA items, continuing without them");
                    response.RefreshedItems = new List<SourcesQaResponse>();
                }

                _logger.LogInformation($"Batch create completed: {response.SuccessCount} successful, {response.FailureCount} failed");

                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSourcesQaBatchAsync");
                throw new InternalServerErrorException("Failed to create sources QA batch", ex);
            }
        }

        private SourcesQaResponse MapToResponse(SourcesQaEntity entity)
        {
            var response = new SourcesQaResponse
            {
                IdPk = entity.IdPk,
                IdOrganizationFk = entity.IdOrganizationFk,
                Title = entity.Title,
                Answer = entity.Answer,
                Timestamp = entity.Timestamp,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt,
                Questions = new List<SourcesQaQuestionResponse>()
            };

            // Parse question IDs and get questions
            if (!string.IsNullOrEmpty(entity.QuestionsIdsFk))
            {
                try
                {
                    var questionIds = entity.QuestionsIdsFk
                        .Split(',')
                        .Where(id => int.TryParse(id.Trim(), out _))
                        .Select(id => int.Parse(id.Trim()))
                        .ToList();

                    if (questionIds.Any())
                    {
                        var questions = _questionsManager.GetQuestionsByIds(questionIds);
                        response.Questions = questions.Select(q => new SourcesQaQuestionResponse
                        {
                            IdPk = q.IdPk,
                            Text = q.Text,
                            Timestamp = q.Timestamp,
                            CreatedAt = q.CreatedAt,
                            UpdatedAt = q.UpdatedAt
                        }).ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Error parsing question IDs for QA {entity.IdPk}: {entity.QuestionsIdsFk}");
                }
            }

            return response;
        }
    }
}
