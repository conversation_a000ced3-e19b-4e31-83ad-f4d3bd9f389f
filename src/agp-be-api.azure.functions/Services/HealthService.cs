using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.Helpers;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services
{
    public class HealthService : IHealthService
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<HealthService> _logger;
        private readonly IConnectionMultiplexer _redis;

        public HealthService(AgpDbSupportContext context, ILogger<HealthService> logger, IConnectionMultiplexer redis = null)
        {
            _context = context;
            _logger = logger;
            _redis = redis;
        }

        public async Task<object> GetHealthStatusAsync()
        {
            try
            {
                var databaseHealth = await CheckDatabaseHealthAsync();
                var redisHealth = await CheckRedisHealthAsync();

                // Cast to dynamic to access Status property
                dynamic dbHealth = databaseHealth;
                dynamic redisHealthDynamic = redisHealth;

                var dbStatus = dbHealth.Status?.ToString() ?? "Unknown";
                var redisStatus = redisHealthDynamic.Status?.ToString() ?? "Unknown";

                var overallStatus = (dbStatus == "Healthy" && (redisStatus == "Healthy" || redisStatus == "Unavailable")) ? "Healthy" : "Unhealthy";

                var healthStatus = new
                {
                    Status = overallStatus,
                    Timestamp = DateTime.UtcNow,
                    Version = ConfigHelper.Version,
                    Environment = ConfigHelper.ENV,
                    Database = databaseHealth,
                    Redis = redisHealth,
                    SwaggerEnabled = ConfigHelper.SwaggerEnabled
                };

                return healthStatus;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking health status");

                return new
                {
                    Status = "Unhealthy",
                    Timestamp = DateTime.UtcNow,
                    Version = ConfigHelper.Version,
                    Environment = ConfigHelper.ENV,
                    Error = ex.Message,
                    Database = new { Status = "Unknown", Error = "Health check failed" },
                    Redis = new { Status = "Unknown", Error = "Health check failed" },
                    SwaggerEnabled = ConfigHelper.SwaggerEnabled
                };
            }
        }

        private async Task<object> CheckDatabaseHealthAsync()
        {
            try
            {
                // Try to connect to the database
                var canConnect = await _context.Database.CanConnectAsync();
                if (canConnect)
                {
                    // Try to execute a simple query
                    var result = await _context.Database.ExecuteSqlRawAsync("SELECT 1");
                    return new
                    {
                        Status = "Healthy",
                        ResponseTime = DateTime.UtcNow
                    };
                }
                else
                {
                    return new { Status = "Unhealthy", Error = "Cannot connect to database" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database health check failed");
                return new { Status = "Unhealthy", Error = ex.Message };
            }
        }

        private async Task<object> CheckRedisHealthAsync()
        {
            try
            {
                if (_redis == null || !_redis.IsConnected)
                {
                    return new { Status = "Unavailable", Message = "Redis not configured or not connected" };
                }

                var database = _redis.GetDatabase();
                var pingResult = await database.PingAsync();

                return new
                {
                    Status = "Healthy",
                    ResponseTime = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis health check failed");
                return new { Status = "Unhealthy", Error = ex.Message };
            }
        }
    }
}
