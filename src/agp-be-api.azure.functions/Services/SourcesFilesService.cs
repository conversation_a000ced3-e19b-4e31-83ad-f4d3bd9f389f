using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO;
using AgpBeApi.Azure.Functions.Exceptions;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SourcesFilesService : ISourcesFilesService
    {
        private readonly ISourcesFilesManager _sourcesFilesManager;
        private readonly ILogger<SourcesFilesService> _logger;
        
        // 1GB limit in bytes (Base64 is ~33% larger than original)
        private const long MAX_FILE_SIZE_BYTES = 1024L * 1024L * 1024L; // 1GB
        private const long MAX_BASE64_SIZE = (long)(MAX_FILE_SIZE_BYTES * 1.33); // ~1.33GB for Base64

        public SourcesFilesService(ISourcesFilesManager sourcesFilesManager, ILogger<SourcesFilesService> logger)
        {
            _sourcesFilesManager = sourcesFilesManager;
            _logger = logger;
        }

        public async Task<CreateSourcesFilesBatchResponse> CreateAsync(
            CreateSourcesFilesBatchRequest batchRequest,
            string idUser,
            string idOrganization)
        {
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(idUser))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new CreateSourcesFilesBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                var entities = new List<SourcesFilesEntity>();

                // Validate all items first
                for (int i = 0; i < batchRequest.Items.Count; i++)
                {
                    var item = batchRequest.Items[i];
                    try
                    {
                        // Validate individual item
                        if (item == null)
                        {
                            response.FailedItems.Add(new BatchFilesErrorItem
                            {
                                Index = i,
                                Error = "Item cannot be null",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate required fields
                        if (string.IsNullOrEmpty(item.FileName))
                        {
                            response.FailedItems.Add(new BatchFilesErrorItem
                            {
                                Index = i,
                                Error = "FileName is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (string.IsNullOrEmpty(item.FileContent))
                        {
                            response.FailedItems.Add(new BatchFilesErrorItem
                            {
                                Index = i,
                                Error = "FileContent is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // CONTROLLO 1GB - Validate file size
                        var base64Length = item.FileContent.Length;
                        if (base64Length > MAX_BASE64_SIZE)
                        {
                            response.FailedItems.Add(new BatchFilesErrorItem
                            {
                                Index = i,
                                Error = $"File too large. Maximum size is 1GB. Current size: {base64Length / (1024 * 1024)} MB",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Calculate actual file size from Base64
                        var actualFileSize = CalculateFileSizeFromBase64(item.FileContent);

                        // Create entity if validation passes
                        var entity = new SourcesFilesEntity
                        {
                            IdUserFk = idUser,
                            IdOrganizationFk = idOrganization,
                            FileName = item.FileName,
                            FileContent = item.FileContent,
                            FileSize = actualFileSize,
                            MimeType = item.MimeType,
                            Description = item.Description,
                            Timestamp = DateTime.UtcNow
                        };

                        entities.Add(entity);
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchFilesErrorItem
                        {
                            Index = i,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                // Insert valid entities in batch
                if (entities.Any())
                {
                    try
                    {
                        var createdEntities = _sourcesFilesManager.CreateSourcesFiles(entities, idUser, idOrganization);
                        response.SuccessfulItems = createdEntities.Select(MapToResponse).ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in batch insert operation");

                        // If batch insert fails, mark all as failed
                        for (int i = 0; i < entities.Count; i++)
                        {
                            response.FailedItems.Add(new BatchFilesErrorItem
                            {
                                Index = i,
                                Error = $"Batch insert failed: {ex.Message}",
                                OriginalItem = batchRequest.Items[i]
                            });
                        }
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list of all items for the organization
                try
                {
                    var refreshedItems = _sourcesFilesManager.GetSourcesFilesByOrganizationIdAsync(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                    _logger.LogInformation($"Added {response.RefreshedItems.Count} refreshed files to response");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed files, continuing without them");
                    response.RefreshedItems = new List<SourcesFilesResponse>();
                }

                _logger.LogInformation($"Batch create completed: {response.SuccessCount} successful, {response.FailureCount} failed");

                return response;
            }
            catch (BadRequestException)
            {
                await Task.Delay(0);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSourcesFilesBatchAsync");
                throw new InternalServerErrorException("Failed to create sources files batch", ex);
            }
        }

        // Helper method to calculate file size from Base64
        private static long CalculateFileSizeFromBase64(string base64String)
        {
            if (string.IsNullOrEmpty(base64String))
                return 0;

            // Remove data URL prefix if present (e.g., "data:image/png;base64,")
            var base64Data = base64String;
            if (base64String.Contains(","))
            {
                base64Data = base64String.Split(',')[1];
            }

            // Calculate actual file size (Base64 is ~33% larger than original)
            var base64Length = base64Data.Length;
            var padding = base64Data.EndsWith("==") ? 2 : base64Data.EndsWith("=") ? 1 : 0;
            return (base64Length * 3 / 4) - padding;
        }

        public async Task<UpdateSourcesFilesBatchResponse> UpdateBatchAsync(UpdateSourcesFilesBatchRequest batchRequest, string userId, string idOrganization)
        {
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new UpdateSourcesFilesBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                // Process each item
                foreach (var item in batchRequest.Items)
                {
                    try
                    {
                        // Validate item
                        if (item.IdPk <= 0)
                        {
                            response.FailedItems.Add(new BatchFilesUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Invalid ID",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // CONTROLLO 1GB per update
                        var base64Length = item.FileContent?.Length ?? 0;
                        if (base64Length > MAX_BASE64_SIZE)
                        {
                            response.FailedItems.Add(new BatchFilesUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = $"File too large. Maximum size is 1GB. Current size: {base64Length / (1024 * 1024)} MB",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Get existing entity
                        var existingEntity = _sourcesFilesManager.GetSourcesFilesByIdAsync(item.IdPk);
                        if (existingEntity == null)
                        {
                            response.FailedItems.Add(new BatchFilesUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Record not found",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Check ownership
                        if (existingEntity.IdUserFk != userId || existingEntity.IdOrganizationFk != idOrganization)
                        {
                            response.FailedItems.Add(new BatchFilesUpdateErrorItem
                            {
                                IdPk = item.IdPk,
                                Error = "Access denied",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Update entity
                        existingEntity.FileName = item.FileName;
                        existingEntity.FileContent = item.FileContent;
                        existingEntity.FileSize = CalculateFileSizeFromBase64(item.FileContent);
                        existingEntity.MimeType = item.MimeType;
                        existingEntity.Description = item.Description;
                        existingEntity.UpdatedAt = DateTime.UtcNow;

                        var updatedEntity = _sourcesFilesManager.UpdateSourcesFiles(existingEntity);
                        response.SuccessfulItems.Add(MapToResponse(updatedEntity));
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchFilesUpdateErrorItem
                        {
                            IdPk = item.IdPk,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list
                try
                {
                    var refreshedItems = _sourcesFilesManager.GetSourcesFilesByOrganizationIdAsync(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed files, continuing without them");
                    response.RefreshedItems = new List<SourcesFilesResponse>();
                }

                _logger.LogInformation($"Batch update completed: {response.SuccessCount} successful, {response.FailureCount} failed");
                return response;
            }
            catch (BadRequestException)
            {
                await Task.Delay(0);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateBatchAsync");
                throw new InternalServerErrorException("Failed to update sources files batch", ex);
            }
        }

        public async Task<DeleteSourcesFilesBatchResponse> DeleteBatchAsync(DeleteSourcesFilesBatchRequest batchRequest, string userId, string idOrganization)
        {
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new DeleteSourcesFilesBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                // Process each item
                foreach (var idPk in batchRequest.Items)
                {
                    try
                    {
                        // Validate ID
                        if (idPk <= 0)
                        {
                            response.FailedItems.Add(new BatchFilesDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Invalid ID"
                            });
                            continue;
                        }

                        // Get existing entity
                        var existingEntity = _sourcesFilesManager.GetSourcesFilesByIdAsync(idPk);
                        if (existingEntity == null)
                        {
                            response.FailedItems.Add(new BatchFilesDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Record not found"
                            });
                            continue;
                        }

                        // Check ownership
                        if (existingEntity.IdUserFk != userId || existingEntity.IdOrganizationFk != idOrganization)
                        {
                            response.FailedItems.Add(new BatchFilesDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Access denied"
                            });
                            continue;
                        }

                        // Delete entity
                        var deleted = _sourcesFilesManager.DeleteSourcesFiles(idPk);
                        if (deleted)
                        {
                            response.SuccessfulItems.Add(idPk);
                        }
                        else
                        {
                            response.FailedItems.Add(new BatchFilesDeleteErrorItem
                            {
                                IdPk = idPk,
                                Error = "Delete operation failed"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchFilesDeleteErrorItem
                        {
                            IdPk = idPk,
                            Error = ex.Message
                        });
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list
                try
                {
                    var refreshedItems = _sourcesFilesManager.GetSourcesFilesByOrganizationIdAsync(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed files, continuing without them");
                    response.RefreshedItems = new List<SourcesFilesResponse>();
                }

                _logger.LogInformation($"Batch delete completed: {response.SuccessCount} successful, {response.FailureCount} failed");
                return response;
            }
            catch (BadRequestException)
            {
                await Task.Delay(0);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in DeleteBatchAsync");
                throw new InternalServerErrorException("Failed to delete sources files batch", ex);
            }
        }

        public async Task<List<SourcesFilesResponse>> GetByOrganizationAsync(string organizationId)
        {
            try
            {
                var entities = _sourcesFilesManager.GetSourcesFilesByOrganizationIdAsync(organizationId);
                return entities.Select(MapToResponse).ToList();
            }
            catch (Exception ex)
            {
                await Task.Delay(0);
                _logger.LogError(ex, "Error in GetSourcesFilesByOrganizationAsync");
                throw new InternalServerErrorException("Failed to get sources files records by organization", ex);
            }
        }

        private static SourcesFilesResponse MapToResponse(SourcesFilesEntity entity)
        {
            return new SourcesFilesResponse
            {
                IdPk = entity.IdPk,
                IdUserFk = entity.IdUserFk,
                IdOrganizationFk = entity.IdOrganizationFk,
                Timestamp = entity.Timestamp,
                FileName = entity.FileName,
                FileContent = entity.FileContent,
                FileSize = entity.FileSize,
                MimeType = entity.MimeType,
                Description = entity.Description,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }
    }
}
