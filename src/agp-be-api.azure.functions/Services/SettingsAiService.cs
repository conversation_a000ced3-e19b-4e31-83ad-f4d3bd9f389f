using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO.Requests.SettingsAiDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SettingsAiDTO;
using AgpBeApi.Azure.Functions.Exceptions;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.Services
{
    public class SettingsAiService : ISettingsAiService
    {
        private readonly ISettingsAiManager _settingsAiManager;
        private readonly ILogger<SettingsAiService> _logger;

        public SettingsAiService(ISettingsAiManager settingsAiManager, ILogger<SettingsAiService> logger)
        {
            _settingsAiManager = settingsAiManager;
            _logger = logger;
        }

        public async Task<CreateSettingsAiBatchResponse> CreateAsync(
            CreateSettingsAiBatchRequest batchRequest,
            string userId,
            string idOrganization)
        {
            await Task.Delay(0); // Per async compliance
            
            try
            {
                // Validate parameters
                if (string.IsNullOrEmpty(userId))
                {
                    throw new BadRequestException("User ID is required");
                }

                if (string.IsNullOrEmpty(idOrganization))
                {
                    throw new BadRequestException("Organization ID is required");
                }

                if (batchRequest == null || batchRequest.Items == null || !batchRequest.Items.Any())
                {
                    throw new BadRequestException("Batch request cannot be null or empty");
                }

                var response = new CreateSettingsAiBatchResponse
                {
                    TotalRequested = batchRequest.Items.Count
                };

                var entities = new List<SettingsAiEntity>();

                // Validate all items first
                for (int i = 0; i < batchRequest.Items.Count; i++)
                {
                    var item = batchRequest.Items[i];
                    try
                    {
                        // Validate individual item
                        if (item == null)
                        {
                            response.FailedItems.Add(new BatchAiErrorItem
                            {
                                Index = i,
                                Error = "Item cannot be null",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate required fields
                        if (string.IsNullOrEmpty(item.ModelName))
                        {
                            response.FailedItems.Add(new BatchAiErrorItem
                            {
                                Index = i,
                                Error = "ModelName is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        if (string.IsNullOrEmpty(item.Instruction))
                        {
                            response.FailedItems.Add(new BatchAiErrorItem
                            {
                                Index = i,
                                Error = "Instruction is required",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Validate temperature range
                        if (item.Temperature < 0.0m || item.Temperature > 1.0m)
                        {
                            response.FailedItems.Add(new BatchAiErrorItem
                            {
                                Index = i,
                                Error = "Temperature must be between 0.0 and 1.0",
                                OriginalItem = item
                            });
                            continue;
                        }

                        // Create entity if validation passes
                        var now = DateTime.UtcNow;
                        var entity = new SettingsAiEntity
                        {
                            IdUserFk = userId,
                            IdOrganizationFk = idOrganization,
                            ModelName = item.ModelName,
                            Instruction = item.Instruction,
                            Temperature = item.Temperature,
                            Timestamp = now,
                            CreatedAt = now,
                            UpdatedAt = now
                        };

                        entities.Add(entity);
                    }
                    catch (Exception ex)
                    {
                        response.FailedItems.Add(new BatchAiErrorItem
                        {
                            Index = i,
                            Error = ex.Message,
                            OriginalItem = item
                        });
                    }
                }

                // Insert valid entities in batch
                if (entities.Any())
                {
                    try
                    {
                        var createdEntities = _settingsAiManager.CreateSettingsAi(entities, userId, idOrganization);
                        response.SuccessfulItems = createdEntities.Select(MapToResponse).ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in batch insert operation");

                        // If batch insert fails, mark all as failed
                        for (int i = 0; i < entities.Count; i++)
                        {
                            response.FailedItems.Add(new BatchAiErrorItem
                            {
                                Index = i,
                                Error = $"Batch insert failed: {ex.Message}",
                                OriginalItem = batchRequest.Items[i]
                            });
                        }
                    }
                }

                response.SuccessCount = response.SuccessfulItems.Count;
                response.FailureCount = response.FailedItems.Count;

                // Get refreshed list of all items for the organization
                try
                {
                    var refreshedItems = _settingsAiManager.GetSettingsAiByOrganizationId(idOrganization);
                    response.RefreshedItems = refreshedItems.Select(MapToResponse).ToList();
                    _logger.LogInformation($"Added {response.RefreshedItems.Count} refreshed AI settings to response");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get refreshed AI settings, continuing without them");
                    response.RefreshedItems = new List<SettingsAiResponse>();
                }

                _logger.LogInformation($"Batch create completed: {response.SuccessCount} successful, {response.FailureCount} failed");

                return response;
            }
            catch (BadRequestException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CreateSettingsAiBatchAsync");
                throw new InternalServerErrorException("Failed to create AI settings batch", ex);
            }
        }

        private static SettingsAiResponse MapToResponse(SettingsAiEntity entity)
        {
            return new SettingsAiResponse
            {
                IdPk = entity.IdPk,
                IdUserFk = entity.IdUserFk,
                IdOrganizationFk = entity.IdOrganizationFk,
                ModelName = entity.ModelName,
                Instruction = entity.Instruction,
                Temperature = entity.Temperature,
                Timestamp = entity.Timestamp,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }
    }
}
