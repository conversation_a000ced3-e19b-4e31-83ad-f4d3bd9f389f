using AgpBeApi.Azure.Functions.DTO;
using AgpBeApi.Azure.Functions.Entities.Session;
using AgpBeApi.Azure.Functions.Helpers;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services
{
    public class TokenValidationService : ITokenValidationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TokenValidationService> _logger;
        private readonly string _authValidationUrl;

        public TokenValidationService(HttpClient httpClient, ILogger<TokenValidationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _authValidationUrl = ConfigHelper.AuthValidationUrl;
        }

        public async Task<GatewayTokenValidationResponse> ValidateTokenAsync(string token)
        {
            try
            {
                var request = new GatewayTokenValidationRequest { Token = token };
                var jsonContent = JsonConvert.SerializeObject(request);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var endpoint = $"{_authValidationUrl}/auth/validate";
                _logger.LogInformation($"Validating token with Gateway at: {endpoint}");

                var response = await _httpClient.PostAsync(endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"Gateway token validation response status: {response.StatusCode}");

                var validationResponse = JsonConvert.DeserializeObject<GatewayTokenValidationResponse>(responseContent);

                if (validationResponse == null)
                {
                    _logger.LogWarning("Failed to deserialize gateway response");
                    return new GatewayTokenValidationResponse
                    {
                        Status = false,
                        Message = "Invalid token",
                        Code = "401"
                    };
                }

                return validationResponse;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error during gateway token validation");
                return new GatewayTokenValidationResponse
                {
                    Status = false,
                    Message = "Token validation service unavailable",
                    Code = "503"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during gateway token validation");
                return new GatewayTokenValidationResponse
                {
                    Status = false,
                    Message = "Token validation failed",
                    Code = "500"
                };
            }
        }
    }
}
