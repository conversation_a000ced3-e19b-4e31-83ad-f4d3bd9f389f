using System.Collections.Generic;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DTO.Requests.SettingsAiDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SettingsAiDTO;

namespace AgpBeApi.Azure.Functions.Services.Interfaces
{
    public interface ISettingsAiService
    {
        Task<CreateSettingsAiBatchResponse> CreateAsync(CreateSettingsAiBatchRequest batchRequest, string userId, string idOrganization);
        Task<UpdateSettingsAiBatchResponse> UpdateBatchAsync(UpdateSettingsAiBatchRequest batchRequest, string userId, string idOrganization);
        Task<DeleteSettingsAiBatchResponse> DeleteBatchAsync(DeleteSettingsAiBatchRequest batchRequest, string userId, string idOrganization);
        Task<List<SettingsAiResponse>> GetByOrganizationAsync(string organizationId);
    }
}
