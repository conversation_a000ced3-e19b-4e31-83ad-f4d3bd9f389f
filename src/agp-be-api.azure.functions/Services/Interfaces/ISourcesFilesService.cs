using System.Collections.Generic;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO;

namespace AgpBeApi.Azure.Functions.Services.Interfaces
{
    public interface ISourcesFilesService
    {
        Task<CreateSourcesFilesBatchResponse> CreateAsync(CreateSourcesFilesBatchRequest batchRequest, string userId, string idOrganization);
        Task<UpdateSourcesFilesBatchResponse> UpdateBatchAsync(UpdateSourcesFilesBatchRequest batchRequest, string userId, string idOrganization);
        Task<DeleteSourcesFilesBatchResponse> DeleteBatchAsync(DeleteSourcesFilesBatchRequest batchRequest, string userId, string idOrganization);
        Task<List<SourcesFilesResponse>> GetByOrganizationAsync(string organizationId);
    }
}
