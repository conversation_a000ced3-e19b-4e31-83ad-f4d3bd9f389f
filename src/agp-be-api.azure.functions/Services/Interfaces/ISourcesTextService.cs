using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services.Interfaces
{
    public interface ISourcesTextService
    {
        Task<SourcesTextResponse> CreateAsync(CreateSourcesTextRequest createRequest);
        Task<SourcesTextResponse> GetByIdAsync(int id);
        Task<List<SourcesTextResponse>> GetByUserAsync(string userId);
        Task<List<SourcesTextResponse>> GetByOrganizationAsync(string organizationId);
        Task<List<SourcesTextResponse>> GetByUserAndOrganizationAsync(string userId, string organizationId);
        Task<SourcesTextResponse> UpdateAsync(int id, UpdateSourcesTextRequest updateRequest);
        Task<bool> DeleteAsync(int id);
        Task<List<SourcesTextResponse>> GetAllAsync();
    }
}
