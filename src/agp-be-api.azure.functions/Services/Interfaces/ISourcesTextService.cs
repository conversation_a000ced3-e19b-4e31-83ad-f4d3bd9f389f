using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Services.Interfaces
{
    public interface ISourcesTextService
    {
        Task<CreateSourcesTextBatchResponse> CreateAsync(CreateSourcesTextBatchRequest batchRequest, string userId, string idOrganization);
        Task<UpdateSourcesTextBatchResponse> UpdateBatchAsync(UpdateSourcesTextBatchRequest batchRequest, string userId, string idOrganization);
        Task<DeleteSourcesTextBatchResponse> DeleteBatchAsync(DeleteSourcesTextBatchRequest batchRequest, string userId, string idOrganization);
        Task<List<SourcesTextResponse>> GetByOrganizationAsync(string organizationId);
    }
}
