using System.Collections.Generic;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;
using AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO;

namespace AgpBeApi.Azure.Functions.Services.Interfaces
{
    public interface ISourcesQaService
    {
        Task<CreateSourcesQaBatchResponse> CreateAsync(CreateSourcesQaBatchRequest batchRequest, string userId, string idOrganization);
        Task<UpdateSourcesQaBatchResponse> UpdateBatchAsync(UpdateSourcesQaBatchRequest batchRequest, string userId, string idOrganization);
        Task<DeleteSourcesQaBatchResponse> DeleteBatchAsync(DeleteSourcesQaBatchRequest batchRequest, string userId, string idOrganization);
        Task<List<SourcesQaResponse>> GetByOrganizationAsync(string organizationId);
    }
}
