using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SettingsAiDTO
{
    public class DeleteSettingsAiBatchResponse
    {
        public List<int> SuccessfulItems { get; set; } = new List<int>();
        public List<BatchAiDeleteErrorItem> FailedItems { get; set; } = new List<BatchAiDeleteErrorItem>();
        public List<SettingsAiResponse> RefreshedItems { get; set; } = new List<SettingsAiResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchAiDeleteErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
    }
}
