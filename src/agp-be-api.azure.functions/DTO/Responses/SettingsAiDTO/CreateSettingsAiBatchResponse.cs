using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SettingsAiDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SettingsAiDTO
{
    public class CreateSettingsAiBatchResponse
    {
        public List<SettingsAiResponse> SuccessfulItems { get; set; } = new List<SettingsAiResponse>();
        public List<BatchAiErrorItem> FailedItems { get; set; } = new List<BatchAiErrorItem>();
        public List<SettingsAiResponse> RefreshedItems { get; set; } = new List<SettingsAiResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchAiErrorItem
    {
        public int Index { get; set; }
        public string Error { get; set; }
        public CreateSettingsAiRequest OriginalItem { get; set; }
    }
}
