using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SettingsAiDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SettingsAiDTO
{
    public class UpdateSettingsAiBatchResponse
    {
        public List<SettingsAiResponse> SuccessfulItems { get; set; } = new List<SettingsAiResponse>();
        public List<BatchAiUpdateErrorItem> FailedItems { get; set; } = new List<BatchAiUpdateErrorItem>();
        public List<SettingsAiResponse> RefreshedItems { get; set; } = new List<SettingsAiResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchAiUpdateErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
        public UpdateSettingsAiItemRequest OriginalItem { get; set; }
    }
}
