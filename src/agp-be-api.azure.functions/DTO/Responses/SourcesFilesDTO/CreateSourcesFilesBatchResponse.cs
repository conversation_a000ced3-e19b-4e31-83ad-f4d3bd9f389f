using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO
{
    public class CreateSourcesFilesBatchResponse
    {
        public List<SourcesFilesResponse> SuccessfulItems { get; set; } = new List<SourcesFilesResponse>();
        public List<BatchFilesErrorItem> FailedItems { get; set; } = new List<BatchFilesErrorItem>();
        public List<SourcesFilesResponse> RefreshedItems { get; set; } = new List<SourcesFilesResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchFilesErrorItem
    {
        public int Index { get; set; }
        public string Error { get; set; }
        public CreateSourcesFilesRequest OriginalItem { get; set; }
    }
}
