using System;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO
{
    public class SourcesFilesResponse
    {
        public int IdPk { get; set; }
        public string IdUserFk { get; set; }
        public string IdOrganizationFk { get; set; }
        public DateTime Timestamp { get; set; }
        public string FileName { get; set; }
        public string FileContent { get; set; }  // Base64 string
        public long FileSize { get; set; }
        public string MimeType { get; set; }
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
