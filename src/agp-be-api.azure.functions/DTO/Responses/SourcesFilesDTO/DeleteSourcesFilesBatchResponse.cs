using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO
{
    public class DeleteSourcesFilesBatchResponse
    {
        public List<int> SuccessfulItems { get; set; } = new List<int>();
        public List<BatchFilesDeleteErrorItem> FailedItems { get; set; } = new List<BatchFilesDeleteErrorItem>();
        public List<SourcesFilesResponse> RefreshedItems { get; set; } = new List<SourcesFilesResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchFilesDeleteErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
    }
}
