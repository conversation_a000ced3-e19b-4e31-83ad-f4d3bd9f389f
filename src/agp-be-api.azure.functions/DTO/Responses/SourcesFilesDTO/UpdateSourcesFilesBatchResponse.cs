using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesFilesDTO
{
    public class UpdateSourcesFilesBatchResponse
    {
        public List<SourcesFilesResponse> SuccessfulItems { get; set; } = new List<SourcesFilesResponse>();
        public List<BatchFilesUpdateErrorItem> FailedItems { get; set; } = new List<BatchFilesUpdateErrorItem>();
        public List<SourcesFilesResponse> RefreshedItems { get; set; } = new List<SourcesFilesResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchFilesUpdateErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
        public UpdateSourcesFilesItemRequest OriginalItem { get; set; }
    }
}
