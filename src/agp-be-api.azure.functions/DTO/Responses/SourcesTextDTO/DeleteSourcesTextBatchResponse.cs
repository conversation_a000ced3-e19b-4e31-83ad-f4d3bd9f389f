using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO
{
    public class DeleteSourcesTextBatchResponse
    {
        public List<int> SuccessfulItems { get; set; } = new List<int>();
        public List<BatchDeleteErrorItem> FailedItems { get; set; } = new List<BatchDeleteErrorItem>();
        public List<SourcesTextResponse> RefreshedItems { get; set; } = new List<SourcesTextResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchDeleteErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
    }
}
