using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO
{
    public class UpdateSourcesTextBatchResponse
    {
        public List<SourcesTextResponse> SuccessfulItems { get; set; } = new List<SourcesTextResponse>();
        public List<BatchUpdateErrorItem> FailedItems { get; set; } = new List<BatchUpdateErrorItem>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchUpdateErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
        public UpdateSourcesTextItemRequest OriginalItem { get; set; }
    }
}
