using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesTextDTO
{
    public class SourcesTextListResponse
    {
        public List<SourcesTextResponse> Items { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
    }

    public class CreateSourcesTextBatchResponse
    {
        public List<SourcesTextResponse> SuccessfulItems { get; set; } = new List<SourcesTextResponse>();
        public List<BatchErrorItem> FailedItems { get; set; } = new List<BatchErrorItem>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchErrorItem
    {
        public int Index { get; set; }
        public string Error { get; set; }
        public CreateSourcesTextRequest OriginalItem { get; set; }
    }
}
