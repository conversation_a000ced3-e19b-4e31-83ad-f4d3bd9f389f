using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO
{
    public class UpdateSourcesQaBatchResponse
    {
        public List<SourcesQaResponse> SuccessfulItems { get; set; } = new List<SourcesQaResponse>();
        public List<BatchQaUpdateErrorItem> FailedItems { get; set; } = new List<BatchQaUpdateErrorItem>();
        public List<SourcesQaResponse> RefreshedItems { get; set; } = new List<SourcesQaResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchQaUpdateErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
        public UpdateSourcesQaItemRequest OriginalItem { get; set; }
    }
}
