using System;
using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO
{
    public class SourcesQaQuestionResponse
    {
        public int IdPk { get; set; }
        public string IdUserFk { get; set; }
        public string Text { get; set; }
        public DateTime Timestamp { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class SourcesQaResponse
    {
        public int IdPk { get; set; }
        public string IdUserFk { get; set; }
        public string IdOrganizationFk { get; set; }
        public List<SourcesQaQuestionResponse> Questions { get; set; } = new List<SourcesQaQuestionResponse>();
        public string Title { get; set; }
        public string Answer { get; set; }
        public DateTime Timestamp { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
