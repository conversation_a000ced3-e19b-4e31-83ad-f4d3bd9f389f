using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO
{
    public class CreateSourcesQaBatchResponse
    {
        public List<SourcesQaResponse> SuccessfulItems { get; set; } = new List<SourcesQaResponse>();
        public List<BatchQaErrorItem> FailedItems { get; set; } = new List<BatchQaErrorItem>();
        public List<SourcesQaResponse> RefreshedItems { get; set; } = new List<SourcesQaResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchQaErrorItem
    {
        public int Index { get; set; }
        public string Error { get; set; }
        public CreateSourcesQaRequest OriginalItem { get; set; }
    }
}
