using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.DTO.Responses.SourcesQaDTO
{
    public class DeleteSourcesQaBatchResponse
    {
        public List<int> SuccessfulItems { get; set; } = new List<int>();
        public List<BatchQaDeleteErrorItem> FailedItems { get; set; } = new List<BatchQaDeleteErrorItem>();
        public List<SourcesQaResponse> RefreshedItems { get; set; } = new List<SourcesQaResponse>();
        public int TotalRequested { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class BatchQaDeleteErrorItem
    {
        public int IdPk { get; set; }
        public string Error { get; set; }
    }
}
