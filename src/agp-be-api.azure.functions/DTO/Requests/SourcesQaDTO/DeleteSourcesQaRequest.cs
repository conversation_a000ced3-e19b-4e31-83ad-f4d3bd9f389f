using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO
{
    public class DeleteSourcesQaBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(50, ErrorMessage = "Maximum 50 items allowed per batch")]
        public List<int> Items { get; set; }
    }
}
