using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesQaDTO
{
    public class UpdateSourcesQaQuestionRequest
    {
        [Required]
        public string Text { get; set; }
    }

    public class UpdateSourcesQaItemRequest
    {
        [Required]
        public int IdPk { get; set; }

        [Required]
        [MinLength(1, ErrorMessage = "At least one question is required")]
        public List<UpdateSourcesQaQuestionRequest> Questions { get; set; }

        [Required]
        [StringLength(2000)]
        public string Title { get; set; }

        [Required]
        public string Answer { get; set; }
    }

    public class UpdateSourcesQaBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(20, ErrorMessage = "Maximum 20 QA items allowed per batch")]
        public List<UpdateSourcesQaItemRequest> Items { get; set; }
    }
}
