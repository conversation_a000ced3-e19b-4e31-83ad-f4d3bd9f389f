using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SettingsAiDTO
{
    public class CreateSettingsAiRequest
    {
        [Required]
        [StringLength(200)]
        public string ModelName { get; set; }

        [Required]
        public string Instruction { get; set; }  // Long text for AI prompt

        [Required]
        [Range(0.0, 1.0, ErrorMessage = "Temperature must be between 0.0 and 1.0")]
        public decimal Temperature { get; set; }
    }

    public class CreateSettingsAiBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(10, ErrorMessage = "Maximum 10 AI settings allowed per batch")]
        public List<CreateSettingsAiRequest> Items { get; set; }
    }
}
