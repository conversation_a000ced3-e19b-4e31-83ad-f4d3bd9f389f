using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO
{
    public class UpdateSourcesTextBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(100, ErrorMessage = "Maximum 100 items allowed per batch")]
        public List<UpdateSourcesTextItemRequest> Items { get; set; }
    }

    public class UpdateSourcesTextItemRequest
    {
        [Required]
        public int IdPk { get; set; }

        [Required]
        [StringLength(2000)]
        public string Title { get; set; }

        [Required]
        public string Text { get; set; }
    }
}
