using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO
{
    public class DeleteSourcesTextBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(100, ErrorMessage = "Maximum 100 items allowed per batch")]
        public List<int> Items { get; set; }
    }
}
