using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesTextDTO
{
    public class CreateSourcesTextRequest
    {
        [Required]
        [StringLength(2000)]
        public string Title { get; set; }

        [Required]
        public string Text { get; set; }
    }

    public class CreateSourcesTextBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(100, ErrorMessage = "Maximum 100 items allowed per batch")]
        public List<CreateSourcesTextRequest> Items { get; set; }
    }
}
