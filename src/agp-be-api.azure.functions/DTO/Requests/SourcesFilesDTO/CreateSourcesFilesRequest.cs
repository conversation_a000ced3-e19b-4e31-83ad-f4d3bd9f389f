using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AgpBeApi.Azure.Functions.DTO.Requests.SourcesFilesDTO
{
    public class CreateSourcesFilesRequest
    {
        [Required]
        [StringLength(500)]
        public string FileName { get; set; }

        [Required]
        public string FileContent { get; set; }  // Base64 string (max 1GB)

        [StringLength(200)]
        public string MimeType { get; set; }

        public string Description { get; set; }
    }

    public class CreateSourcesFilesBatchRequest
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(10, ErrorMessage = "Maximum 10 files allowed per batch")]  // Ridotto per i file
        public List<CreateSourcesFilesRequest> Items { get; set; }
    }
}
