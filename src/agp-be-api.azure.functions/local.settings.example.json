{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "APPLICATIONINSIGHTS_CONNECTION_STRING": "", "FUNCTIONS_WORKER_RUNTIME": "dotnet", "FUNCTIONS_INPROC_NET8_ENABLED": "1", "ENV": "Development", "SWAGGER_ENABLED": "true", "APP_VERSION": "*******", "BUILD_DATE": "2025-07-14T16:30:00Z", "GIT_COMMIT": "local-dev", "BUILD_NUMBER": "dev", "POSTGRESQL_CONNECTION_STRING": "localhost:700", "REDIS_CONNECTION_STRING": "localhost:6379", "AUTH_VALIDATION_URL": "http://localhost:8080", "GATEWAY_ENDPOINT": "localhost:8080", "ROLE_ADMIN_ID": "68724adb3147536156b8a400", "ROLE_READ_ID": "68724adb3147536156b8a401", "ROLE_WRITE_ID": "68724adb3147536156b8a403", "ROLE_DELETE_ID": "68724adb3147536156b8a404"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}