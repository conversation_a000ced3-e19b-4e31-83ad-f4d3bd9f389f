using AgpBeApi.Azure.Functions.Entities.Session;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public static class RoleAuthorizationHelper
    {
        // Metodi HTTP che richiedono permessi specifici
        private static readonly string[] READ_METHODS = { "GET", "HEAD", "OPTIONS" };
        private static readonly string[] WRITE_METHODS = { "POST", "PUT", "PATCH" };
        private static readonly string[] DELETE_METHODS = { "DELETE" };

        /// <summary>
        /// Verifica se l'utente ha i permessi necessari per eseguire la richiesta HTTP
        /// </summary>
        /// <param name="jwtPayload">Payload del JWT con i dati dell'utente</param>
        /// <param name="sessionData">Da<PERSON> della sessione da <PERSON>is</param>
        /// <param name="httpMethod">Metodo HTTP della richiesta</param>
        /// <param name="logger">Logger per tracciare le operazioni</param>
        /// <returns>True se l'utente ha i permessi, False altrimenti</returns>
        public static bool HasPermission(JwtPayload jwtPayload, SessionData sessionData, string httpMethod, ILogger logger)
        {
            try
            {
                if (jwtPayload == null || sessionData?.User == null)
                {
                    logger.LogWarning("JWT payload or session data is null");
                    return false;
                }

                // Ottieni tutti i ruoli dell'utente
                var userRoles = GetUserRoles(jwtPayload, sessionData, logger);
                
                if (userRoles == null || !userRoles.Any())
                {
                    logger.LogWarning($"No roles found for user: {jwtPayload.Email}");
                    return false;
                }

                // Controlla se l'utente ha il ruolo ADMIN (accesso completo)
                if (HasAdminRole(userRoles, logger))
                {
                    logger.LogInformation($"User {jwtPayload.Email} has ADMIN role - access granted");
                    return true;
                }

                // Controlla i permessi specifici in base al metodo HTTP
                return CheckMethodPermissions(userRoles, httpMethod, jwtPayload.Email, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error checking permissions for user: {jwtPayload?.Email}");
                return false;
            }
        }

        /// <summary>
        /// Estrae tutti i ruoli dell'utente dal JWT e dalla sessione
        /// </summary>
        private static string[] GetUserRoles(JwtPayload jwtPayload, SessionData sessionData, ILogger logger)
        {
            try
            {
                // Combina i ruoli dal JWT e dalla sessione
                var jwtRoles = jwtPayload.OrganizationsRoles?.SelectMany(or => or.RoleIds).ToArray() ?? new string[0];
                var sessionRoles = sessionData.User.OrganizationsRoles?.SelectMany(or => or.RoleIds).ToArray() ?? new string[0];

                // Unisce i ruoli e rimuove i duplicati
                var allRoles = jwtRoles.Union(sessionRoles).Distinct().ToArray();

                logger.LogInformation($"User {jwtPayload.Email} has roles: [{string.Join(", ", allRoles)}]");
                return allRoles;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error extracting roles for user: {jwtPayload.Email}");
                return new string[0];
            }
        }

        /// <summary>
        /// Verifica se l'utente ha il ruolo ADMIN
        /// </summary>
        private static bool HasAdminRole(string[] userRoles, ILogger logger)
        {
            var adminRoleId = ConfigHelper.RoleAdminId;
            if (string.IsNullOrEmpty(adminRoleId))
            {
                logger.LogWarning("ROLE_ADMIN_ID not configured");
                return false;
            }

            return userRoles.Contains(adminRoleId);
        }

        /// <summary>
        /// Controlla i permessi specifici in base al metodo HTTP
        /// </summary>
        private static bool CheckMethodPermissions(string[] userRoles, string httpMethod, string userEmail, ILogger logger)
        {
            var method = httpMethod?.ToUpper();

            if (READ_METHODS.Contains(method))
            {
                return HasReadPermission(userRoles, userEmail, logger);
            }
            else if (WRITE_METHODS.Contains(method))
            {
                return HasWritePermission(userRoles, userEmail, logger);
            }
            else if (DELETE_METHODS.Contains(method))
            {
                return HasDeletePermission(userRoles, userEmail, logger);
            }
            else
            {
                logger.LogWarning($"Unknown HTTP method: {method} for user: {userEmail}");
                return false;
            }
        }

        /// <summary>
        /// Verifica se l'utente ha permessi di lettura (READ)
        /// </summary>
        private static bool HasReadPermission(string[] userRoles, string userEmail, ILogger logger)
        {
            var readRoleId = ConfigHelper.RoleReadId;
            if (string.IsNullOrEmpty(readRoleId))
            {
                logger.LogWarning("ROLE_READ_ID not configured");
                return false;
            }

            var hasPermission = userRoles.Contains(readRoleId);
            logger.LogInformation($"User {userEmail} READ permission: {hasPermission}");
            return hasPermission;
        }

        /// <summary>
        /// Verifica se l'utente ha permessi di scrittura (WRITE)
        /// </summary>
        private static bool HasWritePermission(string[] userRoles, string userEmail, ILogger logger)
        {
            var writeRoleId = ConfigHelper.RoleWriteId;
            if (string.IsNullOrEmpty(writeRoleId))
            {
                logger.LogWarning("ROLE_WRITE_ID not configured");
                return false;
            }

            var hasPermission = userRoles.Contains(writeRoleId);
            logger.LogInformation($"User {userEmail} WRITE permission: {hasPermission}");
            return hasPermission;
        }

        /// <summary>
        /// Verifica se l'utente ha permessi di cancellazione (DELETE)
        /// </summary>
        private static bool HasDeletePermission(string[] userRoles, string userEmail, ILogger logger)
        {
            var deleteRoleId = ConfigHelper.RoleDeleteId;
            if (string.IsNullOrEmpty(deleteRoleId))
            {
                logger.LogWarning("ROLE_DELETE_ID not configured");
                return false;
            }

            var hasPermission = userRoles.Contains(deleteRoleId);
            logger.LogInformation($"User {userEmail} DELETE permission: {hasPermission}");
            return hasPermission;
        }

        /// <summary>
        /// Ottiene una descrizione user-friendly del permesso richiesto
        /// </summary>
        public static string GetRequiredPermissionDescription(string httpMethod)
        {
            var method = httpMethod?.ToUpper();

            if (READ_METHODS.Contains(method))
                return "READ permission";
            else if (WRITE_METHODS.Contains(method))
                return "WRITE permission";
            else if (DELETE_METHODS.Contains(method))
                return "DELETE permission";
            else
                return "unknown permission";
        }
    }
}
