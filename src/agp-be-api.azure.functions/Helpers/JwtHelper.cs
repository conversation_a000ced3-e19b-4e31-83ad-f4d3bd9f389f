using Microsoft.Extensions.Logging;
using System;
using System.IdentityModel.Tokens.Jwt;

namespace AgpBeApi.Azure.Functions.Helpers
{
    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class JwtTenatIdNotFoundException : Exception
    {
        public JwtTenatIdNotFoundException(string message) : base(message)
        {
        }
    }

    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class JwtUpnNotFoundException : Exception
    {
        public JwtUpnNotFoundException(string message) : base(message)
        {
        }
    }

    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class JwtAppIdNotFoundException : Exception
    {
        public JwtAppIdNotFoundException(string message) : base(message)
        {
        }
    }

    #pragma warning disable S3925 // "ISerializable" should be implemented correctly
    public class AuthHeaderNotFoundException : Exception
    {
        public AuthHeaderNotFoundException(string message) : base(message)
        {
        }
    }

    /// <summary>
    /// This is the class Jwt Helper.
    /// Used inside FunctionWrapper to retrieve 
    /// the upn and check if the bearer token is set.
    /// </summary>
    public static class JwtHelper
    {
        /// <summary>
        /// This function get the upn from the jwt token.
        /// </summary>
        /// <param name="log"></param>
        /// <param name="authHeader"></param>
        /// <returns>
        /// string upn.
        /// </returns>
        public static string GetUpn(ILogger log, string authHeader)
        {
            try
            {
                if (string.IsNullOrEmpty(authHeader))
                {
                    log.LogError("missing authentication header");
                    throw new AuthHeaderNotFoundException("Missing Authentication Header");
                }
                var token = authHeader.Split(" ")[1];
                var handler = new JwtSecurityTokenHandler();
                var decodedValue = handler.ReadJwtToken(token);

                object upn;
                var res = decodedValue.Payload.TryGetValue("upn", out upn);

                if (res) return upn.ToString();
                else
                {
                    log.LogError("missing upn");
                    var ex = new JwtUpnNotFoundException("error getting upn in bearer token");
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                log.LogInformation("JwtHelper.getUpn ERROR");
                log.LogError(ex.Message, ex);
                throw;
            }
        }
    }
}
