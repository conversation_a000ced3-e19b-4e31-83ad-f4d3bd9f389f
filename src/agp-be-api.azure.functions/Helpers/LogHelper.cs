using System.Text;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.Entities;
using System.Text.Json;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using AgpBeApi.Azure.Functions.FunctionsUtilities;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public class LogHelper
    {
        private readonly ILogger _logger;
        private LogMessage _logInfo;

        public LogHelper(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// This function is used in the wrapper,
        /// before the function runs to
        /// log the start of an HTTP call.
        /// </summary>
        public async Task SetLogInformationStartFunction(string email, HttpRequest req, FunctionWrapper.Func<Task<IActionResult>> azureFunction)
        {
            // Cleaning the classname
            string className = azureFunction.Method.DeclaringType.FullName;
            string apiName = azureFunction.Method.Name;
            string headerHost = req.Headers["Host"];
            string headerPath = req.Path;
            string method = req.Method;
            string rawContent = string.Empty;

            if (req.ContentLength > 0)
            {
                using (StreamReader reader = new StreamReader(req.Body, Encoding.UTF8, true, 1024, true))
                {
                    rawContent = await reader.ReadToEndAsync();
                    req.Body.Position = 0;
                }
            }

            _logInfo = new LogMessage()
            {
                email = email,
                className = className,
                apiName = apiName,
                headerHost = headerHost,
                headerPath = headerPath,
                method = method,
                body = rawContent,
            };

            _logger.LogInformation($"[INFORMATION]: Start http request from user {email} --> {_logInfo.ToString()}");
        }

        /// <summary>
        /// This function is used in the wrapper,
        /// at the end of the function execution to
        /// log the end of the HTTP request call.
        /// </summary>
        public async Task SetLogInformationEndFunction(string email, IActionResult result)
        {
            ObjectResult obj = result as ObjectResult;
            _logger.LogInformation($"[INFORMATION]: End http request from user {email} --> Result of the end http request: {{ \"statusCode\": {obj.StatusCode}, \"data\": \"{obj.Value.ToString()}\" }} | Start http request info: {_logInfo.ToString()}");
            await Task.CompletedTask;
        }
    }
}
