using System;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public static class ConfigHelper
    {
        public static string ENV { get { return Environment.GetEnvironmentVariable("ENV") ?? "Development"; } }
        public static string Version { get { return ServiceUtilities.GetVersion(); } }

        // Swagger Configuration
        public static bool SwaggerEnabled
        {
            get
            {
                var swaggerEnv = Environment.GetEnvironmentVariable("SWAGGER_ENABLED");
                return bool.TryParse(swaggerEnv, out bool result) ? result : false;
            }
        }

        // Database Configuration - PostgreSQL
        public static string PostgreSqlConnectionString
        {
            get { return Environment.GetEnvironmentVariable("POSTGRESQL_CONNECTION_STRING"); }
        }

        // Redis Configuration (if needed)
        public static string RedisConnectionString
        {
            get { return Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING"); }
        }

        // Application Insights
        public static string ApplicationInsightsConnectionString
        {
            get { return Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING"); }
        }

        // Authentication Configuration
        public static string AuthValidationUrl
        {
            get { return Environment.GetEnvironmentVariable("AUTH_VALIDATION_URL") ?? "http://localhost:8080"; }
        }

        // Gateway Configuration
        public static string GatewayEndpoint
        {
            get { return Environment.GetEnvironmentVariable("GATEWAY_ENDPOINT") ?? "localhost:8080"; }
        }

        // Role Configuration
        public static string RoleAdminId
        {
            get { return Environment.GetEnvironmentVariable("ROLE_ADMIN_ID"); }
        }

        public static string RoleReadId
        {
            get { return Environment.GetEnvironmentVariable("ROLE_READ_ID"); }
        }

        public static string RoleWriteId
        {
            get { return Environment.GetEnvironmentVariable("ROLE_WRITE_ID"); }
        }

        public static string RoleDeleteId
        {
            get { return Environment.GetEnvironmentVariable("ROLE_DELETE_ID"); }
        }

        // Build PostgreSQL connection string from individual components if full connection string not provided
        public static string GetPostgreSqlConnectionString()
        {

            return $"{PostgreSqlConnectionString}";
        }

        // Build Redis connection string
        public static string GetRedisConnectionString()
        {
            var connectionString = RedisConnectionString;
            if (!string.IsNullOrEmpty(connectionString))
            {
                return connectionString;
            }

            // Default to localhost:6379
            return "localhost:6379";
        }
    }
}
