using System;
using System.Text.Json;
using System.Text;
using Microsoft.Extensions.Logging;
using AgpBeApi.Azure.Functions.Entities.Session;
using Newtonsoft.Json;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public static class JwtTokenHelper
    {
        public static JwtPayload ParseJwtPayload(string jwt, ILogger logger)
        {
            try
            {
                if (string.IsNullOrEmpty(jwt))
                {
                    logger?.LogWarning("JWT token is null or empty");
                    return null;
                }

                // Split JWT into parts (header.payload.signature)
                var parts = jwt.Split('.');
                if (parts.Length != 3)
                {
                    logger?.LogWarning("Invalid JWT format - expected 3 parts");
                    return null;
                }

                // Decode the payload (second part)
                var payload = parts[1];

                // Add padding if necessary for Base64 decoding
                switch (payload.Length % 4)
                {
                    case 2: payload += "=="; break;
                    case 3: payload += "="; break;
                }

                // Decode from Base64
                var jsonBytes = Convert.FromBase64String(payload);
                var jsonString = Encoding.UTF8.GetString(jsonBytes);

                // Parse JSON to JwtPayload model
                var jwtPayload = JsonConvert.DeserializeObject<JwtPayload>(jsonString);

                if (jwtPayload == null)
                {
                    logger?.LogWarning("Failed to deserialize JWT payload");
                    return null;
                }

                logger?.LogInformation($"JWT parsed successfully for user: {jwtPayload.Email}, session: {jwtPayload.SessionId}");
                return jwtPayload;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error parsing JWT payload");
                return null;
            }
        }


    }
}
