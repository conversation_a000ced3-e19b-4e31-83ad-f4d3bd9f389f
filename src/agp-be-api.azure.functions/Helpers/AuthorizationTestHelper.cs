using AgpBeApi.Azure.Functions.Entities.Session;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace AgpBeApi.Azure.Functions.Helpers
{
    /// <summary>
    /// Helper per testare il sistema di autorizzazione con dati di esempio
    /// </summary>
    public static class AuthorizationTestHelper
    {
        /// <summary>
        /// Crea un JWT payload di test con ruoli specifici
        /// </summary>
        public static JwtPayload CreateTestJwtPayload(string email, string[] roleIds)
        {
            return new JwtPayload
            {
                UserId = "test-user-id",
                Email = email,
                SessionId = "test-session-id",
                OrganizationIds = new List<string> { "test-org-id" },
                OrganizationsRoles = new List<OrganizationRole>
                {
                    new OrganizationRole
                    {
                        OrganizationId = "test-org-id",
                        RoleIds = new List<string>(roleIds)
                    }
                },
                Type = "access",
                Iat = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Exp = DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds(),
                Aud = "test-audience",
                Iss = "test-issuer"
            };
        }

        /// <summary>
        /// Crea dati di sessione di test
        /// </summary>
        public static SessionData CreateTestSessionData(string email, string[] roleIds)
        {
            return new SessionData
            {
                SessionId = "test-session-id",
                SessionCreatedAt = DateTime.UtcNow.AddHours(-1),
                SessionExpiredAt = DateTime.UtcNow.AddHours(1),
                IpAddress = "127.0.0.1",
                UserAgent = "Test-Agent",
                IsRemembered = false,
                CreatedAt = DateTime.UtcNow.AddHours(-1),
                LastActivity = DateTime.UtcNow,
                User = new UserData
                {
                    Id = "test-user-id",
                    Email = email,
                    FirstName = "Test",
                    LastName = "User",
                    OrganizationIds = new List<string> { "test-org-id" },
                    OrganizationsRoles = new List<OrganizationRole>
                    {
                        new OrganizationRole
                        {
                            OrganizationId = "test-org-id",
                            RoleIds = new List<string>(roleIds)
                        }
                    },
                    IsActive = true,
                    IsEmailVerified = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow,
                    LastLogin = DateTime.UtcNow.AddHours(-1)
                }
            };
        }

        /// <summary>
        /// Testa tutti i permessi per un utente con ruoli specifici
        /// </summary>
        public static void TestUserPermissions(string email, string[] roleIds, ILogger logger)
        {
            var jwtPayload = CreateTestJwtPayload(email, roleIds);
            var sessionData = CreateTestSessionData(email, roleIds);

            var methods = new[] { "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS" };

            logger.LogInformation($"=== Testing permissions for user: {email} ===");
            logger.LogInformation($"User roles: [{string.Join(", ", roleIds)}]");

            foreach (var method in methods)
            {
                var hasPermission = RoleAuthorizationHelper.HasPermission(jwtPayload, sessionData, method, logger);
                var requiredPermission = RoleAuthorizationHelper.GetRequiredPermissionDescription(method);
                
                logger.LogInformation($"{method}: {(hasPermission ? "✅ ALLOWED" : "❌ DENIED")} (requires {requiredPermission})");
            }

            logger.LogInformation("=== End permission test ===");
        }

        /// <summary>
        /// Testa scenari comuni di autorizzazione
        /// </summary>
        public static void RunAuthorizationTests(ILogger logger)
        {
            logger.LogInformation("🧪 Starting Authorization Tests...");

            // Test 1: Utente ADMIN (dovrebbe avere accesso a tutto)
            TestUserPermissions("<EMAIL>", new[] { ConfigHelper.RoleAdminId }, logger);

            // Test 2: Utente con solo READ
            TestUserPermissions("<EMAIL>", new[] { ConfigHelper.RoleReadId }, logger);

            // Test 3: Utente con READ + WRITE
            TestUserPermissions("<EMAIL>", new[] { ConfigHelper.RoleReadId, ConfigHelper.RoleWriteId }, logger);

            // Test 4: Utente con tutti i permessi specifici
            TestUserPermissions("<EMAIL>", new[] { 
                ConfigHelper.RoleReadId, 
                ConfigHelper.RoleWriteId, 
                ConfigHelper.RoleDeleteId 
            }, logger);

            // Test 5: Utente senza permessi
            TestUserPermissions("<EMAIL>", new string[0], logger);

            logger.LogInformation("✅ Authorization Tests Completed!");
        }
    }
}
