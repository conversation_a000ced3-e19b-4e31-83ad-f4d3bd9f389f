using AgpBeApi.Azure.Functions.Entities.Session;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public class RedisClient
    {
        private readonly IDatabase _database;
        private readonly ILogger _logger;

        public RedisClient(ILogger logger)
        {
            _logger = logger;
            try
            {
                var connectionString = ConfigHelper.GetRedisConnectionString();
                var connection = ConnectionMultiplexer.Connect(connectionString);
                _database = connection.GetDatabase();
                _logger.LogInformation("Redis connection established successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to Red<PERSON>");
                throw;
            }
        }

        public async Task<SessionData> GetSessionAsync(string sessionId)
        {
            try
            {
                _logger.LogInformation($"Attempting to retrieve session: {sessionId}");
                
                var sessionKey = $"session:{sessionId}";
                var sessionJson = await _database.StringGetAsync(sessionKey);
                
                if (!sessionJson.HasValue)
                {
                    _logger.LogWarning($"Session not found in Redis: {sessionId}");
                    return null;
                }

                var sessionData = JsonConvert.DeserializeObject<SessionData>(sessionJson);
                _logger.LogInformation($"Session retrieved successfully: {sessionId}");
                
                return sessionData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving session from Redis: {sessionId}");
                throw;
            }
        }

        public async Task<bool> SessionExistsAsync(string sessionId)
        {
            try
            {
                var sessionKey = $"session:{sessionId}";
                var exists = await _database.KeyExistsAsync(sessionKey);
                _logger.LogInformation($"Session exists check for {sessionId}: {exists}");
                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking session existence in Redis: {sessionId}");
                throw;
            }
        }
    }
}
