using System;
using System.Reflection;

namespace AgpBeApi.Azure.Functions.Helpers
{
    public class VersionInfo
    {
        public string Version { get; set; }
        public string BuildDate { get; set; }
        public string Environment { get; set; }
        public string GitCommit { get; set; }
        public string BuildNumber { get; set; }
    }

    public static class ServiceUtilities
    {
        public static string GetVersion()
        {
            try
            {
                // Get version from environment variable (local.settings.json or Azure App Settings)
                var envVersion = Environment.GetEnvironmentVariable("APP_VERSION");
                if (!string.IsNullOrEmpty(envVersion))
                {
                    return envVersion;
                }

                return GetAssemblyVersion();
            }
            catch
            {
                return GetAssemblyVersion();
            }
        }

        public static VersionInfo GetVersionInfo()
        {
            try
            {
                // Get all version info from environment variables (local.settings.json or Azure App Settings)
                return new VersionInfo
                {
                    Version = Environment.GetEnvironmentVariable("APP_VERSION") ?? GetAssemblyVersion(),
                    BuildDate = Environment.GetEnvironmentVariable("BUILD_DATE") ?? DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    Environment = Environment.GetEnvironmentVariable("ENV") ?? "Unknown",
                    GitCommit = Environment.GetEnvironmentVariable("GIT_COMMIT") ?? "dev",
                    BuildNumber = Environment.GetEnvironmentVariable("BUILD_NUMBER") ?? "local-dev"
                };
            }
            catch
            {
                return new VersionInfo
                {
                    Version = GetAssemblyVersion(),
                    BuildDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    Environment = "Unknown"
                };
            }
        }

        private static string GetAssemblyVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }
    }
}
