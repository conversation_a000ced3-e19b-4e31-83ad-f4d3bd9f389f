using Newtonsoft.Json;

namespace AgpBeApi.Azure.Functions.Entities.Session
{
    public class GatewayTokenValidationRequest
    {
        [JsonProperty("token")]
        public string Token { get; set; }
    }

    public class GatewayTokenValidationResponse
    {
        [JsonProperty("status")]
        public bool Status { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("data")]
        public object Data { get; set; }
    }
}
