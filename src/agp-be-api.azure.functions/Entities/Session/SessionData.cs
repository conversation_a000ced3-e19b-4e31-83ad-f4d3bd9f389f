using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AgpBeApi.Azure.Functions.Entities.Session
{
    public class SessionData
    {
        [JsonProperty("sessionId")]
        public string SessionId { get; set; }

        [JsonProperty("sessionCreatedAt")]
        public DateTime SessionCreatedAt { get; set; }

        [JsonProperty("sessionExpiredAt")]
        public DateTime SessionExpiredAt { get; set; }

        [JsonProperty("ipAddress")]
        public string IpAddress { get; set; }

        [JsonProperty("userAgent")]
        public string UserAgent { get; set; }

        [JsonProperty("isRemembered")]
        public bool IsRemembered { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("lastActivity")]
        public DateTime LastActivity { get; set; }

        [JsonProperty("user")]
        public UserData User { get; set; }
    }

    public class UserData
    {
        [JsonProperty("organizationsRoles")]
        public List<OrganizationRole> OrganizationsRoles { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("organizationIds")]
        public List<string> OrganizationIds { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("firstName")]
        public string FirstName { get; set; }

        [JsonProperty("lastName")]
        public string LastName { get; set; }

        [JsonProperty("phoneNumber")]
        public string PhoneNumber { get; set; }

        [JsonProperty("isEmailVerified")]
        public bool IsEmailVerified { get; set; }

        [JsonProperty("emailVerificationToken")]
        public string EmailVerificationToken { get; set; }

        [JsonProperty("emailVerificationExpires")]
        public DateTime? EmailVerificationExpires { get; set; }

        [JsonProperty("isActive")]
        public bool IsActive { get; set; }

        [JsonProperty("lastLogin")]
        public DateTime LastLogin { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updatedAt")]
        public DateTime UpdatedAt { get; set; }
    }
}
