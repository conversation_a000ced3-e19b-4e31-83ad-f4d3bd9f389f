using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace AgpBeApi.Azure.Functions.Entities.Session
{
    public class JwtPayload
    {
        [JsonProperty("userId")]
        public string UserId { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("organizationIds")]
        public List<string> OrganizationIds { get; set; }

        [JsonProperty("organizationsRoles")]
        public List<OrganizationRole> OrganizationsRoles { get; set; }

        [JsonProperty("sessionId")]
        public string SessionId { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("iat")]
        public long Iat { get; set; }

        [JsonProperty("exp")]
        public long Exp { get; set; }

        [JsonProperty("aud")]
        public string Aud { get; set; }

        [JsonProperty("iss")]
        public string Iss { get; set; }
    }

    public class OrganizationRole
    {
        [JsonProperty("organizationId")]
        public string OrganizationId { get; set; }

        [JsonProperty("roleIds")]
        public List<string> RoleIds { get; set; }
    }
}
