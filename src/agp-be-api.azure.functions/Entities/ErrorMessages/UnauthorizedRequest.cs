namespace AgpBeApi.Azure.Functions.Entities.ErrorMessages
{
    /// <summary>
    /// Return a Unauthorized request custom message (error 401).
    /// </summary>
    public class UnauthorizedRequest
    {
        public bool status { get; set; }
        public string code { get; set; }
        public string message { get; set; }
        public string data { get; set; }

        /// <summary>
        /// Return a formatted custom Unauthorized message (error 401).
        /// </summary>
        /// <param name="msg"> Message </param>
        public UnauthorizedRequest(string msg)
        {
            status = false;
            code= "401";
            message = msg;
            data = null;
        }
    }
}
