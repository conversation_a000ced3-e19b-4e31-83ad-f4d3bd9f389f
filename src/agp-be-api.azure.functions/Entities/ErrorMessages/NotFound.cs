namespace AgpBeApi.Azure.Functions.Entities.ErrorMessages
{
    /// <summary>
    /// Return a NotFound request custom message (error 404).
    /// </summary>
    public class NotFound
    {
        public bool status { get; set; }
        public string code { get; set; }
        public string message { get; set; }
        public string data { get; set; }

        /// <summary>
        /// Return a formatted custom Unauthorized message (error 401).
        /// </summary>
        /// <param name="msg"> Message </param>
        public NotFound(string msg)
        {
            status = false;
            code= "404";
            message = msg;
            data = null;
        }
    }
}
