namespace AgpBeApi.Azure.Functions.Entities.ErrorMessages
{
    /// <summary>
    /// Return a bad request custom message (error 400).
    /// </summary>
    public class BadRequest
    {
        public bool status { get; set; }
        public string code { get; set; }
        public string message { get; set; }
        public string data { get; set; }

        /// <summary>
        /// Return a formatted custom Unauthorized message (error 401).
        /// </summary>
        /// <param name="msg"> Message </param>
        public BadRequest(string msg)
        {
            status = false;
            code= "400";
            message = msg;
            data = null;
        }
    }
}
