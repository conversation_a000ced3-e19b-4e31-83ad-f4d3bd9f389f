namespace AgpBeApi.Azure.Functions.Entities.ErrorMessages
{
    /// <summary>
    /// Return a Internal Server Error custom message (error 500).
    /// </summary>
    public class InternalServerErrorResponse
    {
        public bool status { get; set; }
        public string code { get; set; }
        public string message { get; set; }
        public string data { get; set; }

        /// <summary>
        /// Return a formatted custom Unauthorized message (error 401).
        /// </summary>
        /// <param name="msg"> Message </param>
        public InternalServerErrorResponse(string msg)
        {
            status = false;
            code= "500";
            message = msg;
            data = null;
        }
    }
}
