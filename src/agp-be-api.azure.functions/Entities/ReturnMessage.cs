namespace AgpBeApi.Azure.Functions.Entities
{
    public class ReturnMessage<T>
    {
        public bool Status { get; set; }
        public string Message { get; set; }
        public string Code { get; set; }
        public T Data { get; set; }

        public ReturnMessage(bool status, string message, string code, T data)
        {
            Status = status;
            Message = message;
            Code = code;
            Data = data;
        }
    }
}
