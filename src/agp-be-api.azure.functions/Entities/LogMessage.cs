using System.Text.Json;

namespace AgpBeApi.Azure.Functions.Entities
{
    public class LogMessage
    {
        public string email { get; set; }
        public string className { get; set; }
        public string apiName { get; set; }
        public string headerHost { get; set; }
        public string headerPath { get; set; }
        public string method { get; set; }
        public string body { get; set; }

        public override string ToString()
        {
            return JsonSerializer.Serialize(this);
        }
    }
}
