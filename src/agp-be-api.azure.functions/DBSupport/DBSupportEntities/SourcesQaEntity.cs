using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities
{
    [Table("sources_qa")]
    public class SourcesQaEntity
    {
        [Key]
        [Column("id_pk")]
        public int IdPk { get; set; }

        [Required]
        [Column("id_organization_fk")]
        [StringLength(50)]
        public string IdOrganizationFk { get; set; }

        [Required]
        [Column("questions_ids_fk")]
        public string QuestionsIdsFk { get; set; }  // IDs separati da virgola

        [Required]
        [Column("title")]
        [StringLength(2000)]
        public string Title { get; set; }

        [Required]
        [Column("answer")]
        public string Answer { get; set; }

        [Required]
        [Column("timestamp")]
        public DateTime Timestamp { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
