using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities
{
    [Table("sources_files")]
    public class SourcesFilesEntity
    {
        [Key]
        [Column("id_pk")]
        public int IdPk { get; set; }

        [Required]
        [Column("id_user_fk")]
        [StringLength(50)]
        public string IdUserFk { get; set; }

        [Required]
        [Column("id_organization_fk")]
        [StringLength(50)]
        public string IdOrganizationFk { get; set; }

        [Required]
        [Column("timestamp")]
        public DateTime Timestamp { get; set; }

        [Required]
        [Column("file_name")]
        [StringLength(500)]
        public string FileName { get; set; }

        [Required]
        [Column("file_content")]
        public string FileContent { get; set; }  // Base64 string

        [Required]
        [Column("file_size")]
        public long FileSize { get; set; }

        [Column("mime_type")]
        [StringLength(200)]
        public string MimeType { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
