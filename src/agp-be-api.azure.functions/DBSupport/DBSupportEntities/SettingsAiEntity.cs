using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities
{
    [Table("settings_ai")]
    public class SettingsAiEntity
    {
        [Key]
        [Column("id_pk")]
        public int IdPk { get; set; }

        [Required]
        [Column("id_user_fk")]
        [StringLength(50)]
        public string IdUserFk { get; set; }

        [Required]
        [Column("id_organization_fk")]
        [StringLength(50)]
        public string IdOrganizationFk { get; set; }

        [Required]
        [Column("model_name")]
        [StringLength(200)]
        public string ModelName { get; set; }

        [Required]
        [Column("instruction")]
        public string Instruction { get; set; }  // Long text for AI prompt

        [Required]
        [Column("temperature")]
        public decimal Temperature { get; set; }  // Range 0.0 to 1.0

        [Required]
        [Column("timestamp")]
        public DateTime Timestamp { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
