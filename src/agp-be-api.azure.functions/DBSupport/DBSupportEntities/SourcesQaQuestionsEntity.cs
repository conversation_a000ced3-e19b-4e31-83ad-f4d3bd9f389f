using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities
{
    [Table("sources_qa_questions")]
    public class SourcesQaQuestionsEntity
    {
        [Key]
        [Column("id_pk")]
        public int IdPk { get; set; }

        [Required]
        [Column("text")]
        public string Text { get; set; }

        [Required]
        [Column("timestamp")]
        public DateTime Timestamp { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
