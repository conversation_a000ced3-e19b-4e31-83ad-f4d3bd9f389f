using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities
{
    [Table("sources_text")]
    public class SourcesTextEntity
    {
        [Key]
        [Column("id_pk")]
        public int IdPk { get; set; }

        [Required]
        [Column("id_user_fk")]
        public string IdUserFk { get; set; }

        [Required]
        [Column("id_organization_fk")]
        public string IdOrganizationFk { get; set; }

        [Required]
        [Column("timestamp")]
        public DateTime Timestamp { get; set; }

        [Required]
        [Column("title")]
        [StringLength(2000)]
        public string Title { get; set; }

        [Required]
        [Column("text")]
        public string Text { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
