using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.Helpers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportContext
{
    public class AgpDbSupportContext : DbContext
    {
        public DbSet<SourcesTextEntity> SourcesText { get; set; }

        public AgpDbSupportContext(DbContextOptions<AgpDbSupportContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseNpgsql(ConfigHelper.GetPostgreSqlConnectionString());
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure SourcesTextEntity
            modelBuilder.Entity<SourcesTextEntity>(entity =>
            {
                entity.HasKey(e => e.IdPk);
                
                entity.Property(e => e.IdPk)
                    .ValueGeneratedOnAdd();

                entity.Property(e => e.IdUserFk)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.IdOrganizationFk)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.Timestamp)
                    .IsRequired();

                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasMaxLength(2000);

                entity.Property(e => e.Text)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedAt);

                // Indexes for better query performance
                entity.HasIndex(e => e.IdUserFk)
                    .HasDatabaseName("IX_SourcesText_IdUserFk");

                entity.HasIndex(e => e.IdOrganizationFk)
                    .HasDatabaseName("IX_SourcesText_IdOrganizationFk");

                entity.HasIndex(e => new { e.IdUserFk, e.IdOrganizationFk })
                    .HasDatabaseName("IX_SourcesText_IdUserFk_IdOrganizationFk");

                entity.HasIndex(e => e.Timestamp)
                    .HasDatabaseName("IX_SourcesText_Timestamp");
            });
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<SourcesTextEntity>();

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Modified)
                {
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
