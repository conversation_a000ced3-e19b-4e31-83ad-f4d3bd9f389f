using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.Helpers;
using Microsoft.EntityFrameworkCore;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportContext
{
    public class AgpDbSupportContext : DbContext
    {
        // Sources Management Domain
        public DbSet<SourcesTextEntity> SourcesText { get; set; }
        public DbSet<SourcesFilesEntity> SourcesFiles { get; set; }
        public DbSet<SourcesQaEntity> SourcesQa { get; set; }
        public DbSet<SourcesQaQuestionsEntity> SourcesQaQuestions { get; set; }

        // Future domains can be added here following the same pattern
        // public DbSet<SourcesImageEntity> SourcesImage { get; set; }
        // public DbSet<SourcesVideoEntity> SourcesVideo { get; set; }
        // public DbSet<OrganizationEntity> Organizations { get; set; }
        // public DbSet<UserEntity> Users { get; set; }

        public AgpDbSupportContext(DbContextOptions<AgpDbSupportContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
            => optionsBuilder.UseNpgsql(ConfigHelper.GetPostgreSqlConnectionString());

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure all entities using explicit table mapping
            modelBuilder.Entity<SourcesTextEntity>().ToTable("sources_text");
            modelBuilder.Entity<SourcesFilesEntity>().ToTable("sources_files");
            modelBuilder.Entity<SourcesQaEntity>().ToTable("sources_qa");
            modelBuilder.Entity<SourcesQaQuestionsEntity>().ToTable("sources_qa_questions");
        }
    }
}
