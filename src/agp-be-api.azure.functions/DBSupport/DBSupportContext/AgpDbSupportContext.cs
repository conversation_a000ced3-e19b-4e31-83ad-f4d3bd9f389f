using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.Helpers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportContext
{
    public class AgpDbSupportContext : DbContext
    {
        public DbSet<SourcesTextEntity> SourcesText { get; set; }

        public AgpDbSupportContext(DbContextOptions<AgpDbSupportContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseNpgsql(ConfigHelper.GetPostgreSqlConnectionString(), options =>
                {
                    options.CommandTimeout(30); // 30 seconds timeout
                    options.EnableRetryOnFailure(3); // Retry 3 times on failure
                });
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entities following your pattern
            ConfigureSourcesText(modelBuilder);

            // Future entities can be added here
            // ConfigureSourcesImage(modelBuilder);
            // ConfigureOrganizations(modelBuilder);
        }

        private static void ConfigureSourcesText(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SourcesTextEntity>(entity =>
            {
                // Explicit table mapping (following your pattern)
                entity.ToTable("sources_text");

                // Primary key
                entity.HasKey(e => e.IdPk);

                // Column mappings with explicit names
                entity.Property(e => e.IdPk)
                    .HasColumnName("id_pk")
                    .ValueGeneratedOnAdd();

                entity.Property(e => e.IdUserFk)
                    .HasColumnName("id_user_fk")
                    .IsRequired()
                    .HasMaxLength(50); // Reduced for better performance

                entity.Property(e => e.IdOrganizationFk)
                    .HasColumnName("id_organization_fk")
                    .IsRequired()
                    .HasMaxLength(50); // Reduced for better performance

                entity.Property(e => e.Timestamp)
                    .HasColumnName("timestamp")
                    .IsRequired();

                entity.Property(e => e.Title)
                    .HasColumnName("title")
                    .IsRequired()
                    .HasMaxLength(2000);

                entity.Property(e => e.Text)
                    .HasColumnName("text")
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnName("updated_at");

                // Performance-optimized indexes
                entity.HasIndex(e => e.IdOrganizationFk)
                    .HasDatabaseName("idx_sources_text_organization");

                entity.HasIndex(e => e.IdUserFk)
                    .HasDatabaseName("idx_sources_text_user");

                entity.HasIndex(e => new { e.IdOrganizationFk, e.Timestamp })
                    .HasDatabaseName("idx_sources_text_org_timestamp")
                    .IsDescending(false, true); // Organization ASC, Timestamp DESC

                entity.HasIndex(e => new { e.IdUserFk, e.IdOrganizationFk })
                    .HasDatabaseName("idx_sources_text_user_org");
            });
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<SourcesTextEntity>();

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Modified)
                {
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
