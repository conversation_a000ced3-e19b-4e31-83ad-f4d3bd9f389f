using System;
using System.Collections.Generic;
using System.Linq;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SourcesQaQuestionsManager : ISourcesQaQuestionsManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SourcesQaQuestionsManager> _logger;

        public SourcesQaQuestionsManager(AgpDbSupportContext context, ILogger<SourcesQaQuestionsManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public List<SourcesQaQuestionsEntity> CreateQuestions(List<SourcesQaQuestionsEntity> questions)
        {
            try
            {
                if (questions == null || questions.Count == 0)
                {
                    return new List<SourcesQaQuestionsEntity>();
                }

                // Set timestamps for all questions (force UTC)
                var now = DateTime.UtcNow;
                foreach (var question in questions)
                {
                    question.CreatedAt = now;
                    question.UpdatedAt = now;
                    question.Timestamp = now;

                    // Force UTC for any existing DateTime values
                    if (question.Timestamp.Kind != DateTimeKind.Utc)
                    {
                        question.Timestamp = DateTime.SpecifyKind(question.Timestamp, DateTimeKind.Utc);
                    }
                }

                // Log per debug
                _logger.LogInformation($"Creating {questions.Count} QA questions");
                foreach (var q in questions)
                {
                    _logger.LogInformation($"Question Text: '{q.Text?.Substring(0, Math.Min(50, q.Text?.Length ?? 0))}'");
                }

                // Simple add and save (sincrono come richiesto)
                _context.SourcesQaQuestions.AddRange(questions);
                _context.SaveChanges();

                _logger.LogInformation($"Created {questions.Count} QA questions");
                return questions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {questions?.Count ?? 0} QA questions");
                throw;
            }
        }

        public SourcesQaQuestionsEntity GetQuestionById(int id)
        {
            try
            {
                // Use Find for primary key - much faster
                return _context.SourcesQaQuestions.Find(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving QA question with ID: {id}");
                return null;
            }
        }

        public List<SourcesQaQuestionsEntity> GetQuestionsByIds(List<int> ids)
        {
            try
            {
                if (ids == null || ids.Count == 0)
                {
                    return new List<SourcesQaQuestionsEntity>();
                }

                var result = _context.SourcesQaQuestions
                    .Where(q => ids.Contains(q.IdPk))
                    .OrderByDescending(q => q.IdPk) // Dal più grande al più piccolo
                    .ToList();
                
                _logger.LogInformation($"Retrieved {result.Count} QA questions by IDs");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving QA questions by IDs");
                return new List<SourcesQaQuestionsEntity>();
            }
        }

        public SourcesQaQuestionsEntity UpdateQuestion(SourcesQaQuestionsEntity question)
        {
            try
            {
                // Force all DateTime to UTC to avoid PostgreSQL issues
                question.UpdatedAt = DateTime.UtcNow;
                question.CreatedAt = DateTime.SpecifyKind(question.CreatedAt, DateTimeKind.Utc);
                question.Timestamp = DateTime.SpecifyKind(question.Timestamp, DateTimeKind.Utc);
                
                _context.SourcesQaQuestions.Update(question);
                _context.SaveChanges();
                
                _logger.LogInformation($"Updated QA question with ID: {question.IdPk}");
                return question;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating QA question with ID: {question.IdPk}");
                throw;
            }
        }

        public bool DeleteQuestion(int id)
        {
            try
            {
                var entity = _context.SourcesQaQuestions.Find(id);
                if (entity == null)
                {
                    return false;
                }

                _context.SourcesQaQuestions.Remove(entity);
                _context.SaveChanges();
                
                _logger.LogInformation($"Deleted QA question with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting QA question with ID: {id}");
                throw;
            }
        }
    }
}
