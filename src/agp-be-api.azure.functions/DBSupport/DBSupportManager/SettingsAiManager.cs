using System;
using System.Collections.Generic;
using System.Linq;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SettingsAiManager : ISettingsAiManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SettingsAiManager> _logger;

        public SettingsAiManager(AgpDbSupportContext context, ILogger<SettingsAiManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public List<SettingsAiEntity> CreateSettingsAi(
            List<SettingsAiEntity> settingsAiEntities,
            string idUser,
            string idOrganization)
        {
            try
            {
                if (settingsAiEntities == null || settingsAiEntities.Count == 0)
                {
                    return new List<SettingsAiEntity>();
                }

                // Set timestamps, user and organization info for all entities (force UTC)
                var now = DateTime.UtcNow;
                foreach (var entity in settingsAiEntities)
                {
                    entity.CreatedAt = now;
                    entity.UpdatedAt = now;
                    entity.IdUserFk = idUser;
                    entity.IdOrganizationFk = idOrganization;
                    entity.Timestamp = now;
                    
                    // Force UTC for any existing DateTime values
                    if (entity.Timestamp.Kind != DateTimeKind.Utc)
                    {
                        entity.Timestamp = DateTime.SpecifyKind(entity.Timestamp, DateTimeKind.Utc);
                    }
                }

                // Simple add and save (sincrono come richiesto)
                _context.SettingsAi.AddRange(settingsAiEntities);
                _context.SaveChanges();

                _logger.LogInformation($"Created {settingsAiEntities.Count} AI settings records");
                return settingsAiEntities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {settingsAiEntities?.Count ?? 0} AI settings records");
                throw;
            }
        }

        public SettingsAiEntity GetSettingsAiById(int id)
        {
            try
            {
                // Use Find for primary key - much faster
                return _context.SettingsAi.Find(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving AI settings record with ID: {id}");
                return null;
            }
        }

        public List<SettingsAiEntity> GetSettingsAiByOrganizationId(string organizationId)
        {
            try
            {
                _logger.LogInformation($"Query for organization: {organizationId}");
                
                // Query with ordering by IdPk DESC (newest first)
                var result = _context.SettingsAi
                    .Where(d => d.IdOrganizationFk == organizationId)
                    .OrderByDescending(d => d.IdPk) // Dal più grande al più piccolo
                    .Take(50) // Limit for AI settings
                    .ToList();
                
                _logger.LogInformation($"Retrieved {result.Count} AI settings for organization: {organizationId} ordered by IdPk DESC");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in query for organization: {organizationId}");
                return new List<SettingsAiEntity>();
            }
        }

        public SettingsAiEntity UpdateSettingsAi(SettingsAiEntity settingsAi)
        {
            try
            {
                // Force all DateTime to UTC to avoid PostgreSQL issues
                settingsAi.UpdatedAt = DateTime.UtcNow;
                settingsAi.CreatedAt = DateTime.SpecifyKind(settingsAi.CreatedAt, DateTimeKind.Utc);
                settingsAi.Timestamp = DateTime.SpecifyKind(settingsAi.Timestamp, DateTimeKind.Utc);
                
                _context.SettingsAi.Update(settingsAi);
                _context.SaveChanges();
                
                _logger.LogInformation($"Updated AI settings record with ID: {settingsAi.IdPk}");
                return settingsAi;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating AI settings record with ID: {settingsAi.IdPk}");
                throw;
            }
        }

        public bool DeleteSettingsAi(int id)
        {
            try
            {
                var entity = _context.SettingsAi.Find(id);
                if (entity == null)
                {
                    return false;
                }

                _context.SettingsAi.Remove(entity);
                _context.SaveChanges();
                
                _logger.LogInformation($"Deleted AI settings record with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting AI settings record with ID: {id}");
                throw;
            }
        }
    }
}
