using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces
{
    public interface ISourcesQaQuestionsManager
    {
        List<SourcesQaQuestionsEntity> CreateQuestions(List<SourcesQaQuestionsEntity> questions, string idUser);
        SourcesQaQuestionsEntity GetQuestionById(int id);
        List<SourcesQaQuestionsEntity> GetQuestionsByIds(List<int> ids);
        SourcesQaQuestionsEntity UpdateQuestion(SourcesQaQuestionsEntity question);
        bool DeleteQuestion(int id);
    }
}
