using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces
{
    public interface ISettingsAiManager
    {
        List<SettingsAiEntity> CreateSettingsAi(List<SettingsAiEntity> settingsAiEntities, string idUser, string idOrganization);
        SettingsAiEntity GetSettingsAiById(int id);
        List<SettingsAiEntity> GetSettingsAiByOrganizationId(string organizationId);
        SettingsAiEntity UpdateSettingsAi(SettingsAiEntity settingsAi);
        bool DeleteSettingsAi(int id);
    }
}
