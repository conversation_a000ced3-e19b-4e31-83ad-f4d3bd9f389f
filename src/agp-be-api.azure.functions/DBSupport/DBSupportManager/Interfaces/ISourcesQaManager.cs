using System.Collections.Generic;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces
{
    public interface ISourcesQaManager
    {
        List<SourcesQaEntity> CreateSourcesQa(List<SourcesQaEntity> sourcesQaEntities, string idOrganization);
        SourcesQaEntity GetSourcesQaById(int id);
        List<SourcesQaEntity> GetSourcesQaByOrganizationId(string organizationId);
        SourcesQaEntity UpdateSourcesQa(SourcesQaEntity sourcesQa);
        bool DeleteSourcesQa(int id);
    }
}
