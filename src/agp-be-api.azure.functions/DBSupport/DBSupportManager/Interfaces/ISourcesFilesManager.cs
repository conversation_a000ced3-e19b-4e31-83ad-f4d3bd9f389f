using System.Collections.Generic;
using System.Threading.Tasks;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces
{
    public interface ISourcesFilesManager
    {
        List<SourcesFilesEntity> CreateSourcesFiles(List<SourcesFilesEntity> sourcesFilesEntities, string idUser, string idOrganization);
        SourcesFilesEntity GetSourcesFilesByIdAsync(int id);
        List<SourcesFilesEntity> GetSourcesFilesByOrganizationIdAsync(string organizationId);
        SourcesFilesEntity UpdateSourcesFiles(SourcesFilesEntity sourcesFiles);
        bool DeleteSourcesFiles(int id);
    }
}
