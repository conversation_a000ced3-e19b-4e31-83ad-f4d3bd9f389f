using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces
{
    public interface ISourcesTextManager
    {
        Task<List<SourcesTextEntity>> CreateSourcesTextAsync(List<SourcesTextEntity> sourcesTextEntities, string idUser, string idOrganization);
        Task<SourcesTextEntity> GetSourcesTextByIdAsync(int id);
        Task<List<SourcesTextEntity>> GetSourcesTextByUserIdAsync(string userId);
        Task<List<SourcesTextEntity>> GetSourcesTextByOrganizationIdAsync(string organizationId);
        Task<List<SourcesTextEntity>> GetSourcesTextByUserAndOrganizationAsync(string userId, string organizationId);
        Task<SourcesTextEntity> UpdateSourcesTextAsync(SourcesTextEntity sourcesText);
        Task<bool> DeleteSourcesTextAsync(int id);
        Task<List<SourcesTextEntity>> GetAllSourcesTextAsync();
    }
}
