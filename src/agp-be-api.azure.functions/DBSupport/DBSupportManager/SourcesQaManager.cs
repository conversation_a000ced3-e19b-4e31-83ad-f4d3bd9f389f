using System;
using System.Collections.Generic;
using System.Linq;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SourcesQaManager : ISourcesQaManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SourcesQaManager> _logger;

        public SourcesQaManager(AgpDbSupportContext context, ILogger<SourcesQaManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public List<SourcesQaEntity> CreateSourcesQa(List<SourcesQaEntity> sourcesQaEntities, string idUser, string idOrganization)
        {
            try
            {
                if (sourcesQaEntities == null || sourcesQaEntities.Count == 0)
                {
                    return new List<SourcesQaEntity>();
                }

                // Set timestamps, user and organization info for all entities (force UTC)
                var now = DateTime.UtcNow;
                foreach (var entity in sourcesQaEntities)
                {
                    entity.CreatedAt = now;
                    entity.UpdatedAt = now;
                    entity.IdUserFk = idUser;
                    entity.IdOrganizationFk = idOrganization;
                    entity.Timestamp = now;

                    // Force UTC for any existing DateTime values
                    if (entity.Timestamp.Kind != DateTimeKind.Utc)
                    {
                        entity.Timestamp = DateTime.SpecifyKind(entity.Timestamp, DateTimeKind.Utc);
                    }
                }

                // Simple add and save (sincrono come richiesto)
                _context.SourcesQa.AddRange(sourcesQaEntities);
                _context.SaveChanges();

                _logger.LogInformation($"Created {sourcesQaEntities.Count} sources QA records");
                return sourcesQaEntities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {sourcesQaEntities?.Count ?? 0} sources QA records");
                throw;
            }
        }

        public SourcesQaEntity GetSourcesQaById(int id)
        {
            try
            {
                // Use Find for primary key - much faster
                return _context.SourcesQa.Find(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources QA record with ID: {id}");
                return null;
            }
        }

        public List<SourcesQaEntity> GetSourcesQaByOrganizationId(string organizationId)
        {
            try
            {
                _logger.LogInformation($"Query for organization: {organizationId}");
                
                // Query with ordering by IdPk DESC (newest first)
                var result = _context.SourcesQa
                    .Where(d => d.IdOrganizationFk == organizationId)
                    .OrderByDescending(d => d.IdPk) // Dal più grande al più piccolo
                    .Take(50) // Limit for QA records
                    .ToList();
                
                _logger.LogInformation($"Retrieved {result.Count} QA records for organization: {organizationId} ordered by IdPk DESC");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in query for organization: {organizationId}");
                return new List<SourcesQaEntity>();
            }
        }

        public SourcesQaEntity UpdateSourcesQa(SourcesQaEntity sourcesQa)
        {
            try
            {
                // Force all DateTime to UTC to avoid PostgreSQL issues
                sourcesQa.UpdatedAt = DateTime.UtcNow;
                sourcesQa.CreatedAt = DateTime.SpecifyKind(sourcesQa.CreatedAt, DateTimeKind.Utc);
                sourcesQa.Timestamp = DateTime.SpecifyKind(sourcesQa.Timestamp, DateTimeKind.Utc);
                
                _context.SourcesQa.Update(sourcesQa);
                _context.SaveChanges();
                
                _logger.LogInformation($"Updated sources QA record with ID: {sourcesQa.IdPk}");
                return sourcesQa;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating sources QA record with ID: {sourcesQa.IdPk}");
                throw;
            }
        }

        public bool DeleteSourcesQa(int id)
        {
            try
            {
                var entity = _context.SourcesQa.Find(id);
                if (entity == null)
                {
                    return false;
                }

                _context.SourcesQa.Remove(entity);
                _context.SaveChanges();
                
                _logger.LogInformation($"Deleted sources QA record with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting sources QA record with ID: {id}");
                throw;
            }
        }
    }
}
