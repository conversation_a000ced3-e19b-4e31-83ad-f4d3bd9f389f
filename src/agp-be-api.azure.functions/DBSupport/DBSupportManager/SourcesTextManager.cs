using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SourcesTextManager : ISourcesTextManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SourcesTextManager> _logger;

        public SourcesTextManager(AgpDbSupportContext context, ILogger<SourcesTextManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<SourcesTextEntity>> CreateSourcesTextAsync(
            List<SourcesTextEntity> sourcesTextEntities,
            string idUser,
            string idOrganization
        )
        {
            try
            {
                if (sourcesTextEntities == null || sourcesTextEntities.Count == 0)
                {
                    return new List<SourcesTextEntity>();
                }

                // Set timestamps for all entities
                var now = DateTime.UtcNow;
                foreach (var entity in sourcesTextEntities)
                {
                    entity.CreatedAt = now;
                    entity.UpdatedAt = now;
                    entity.IdUserFk = idUser;
                    entity.IdOrganizationFk = idOrganization;
                }

                // Try simple add and save with timeout handling
                try
                {
                    _context.SourcesText.AddRange(sourcesTextEntities);

                    // Use cancellation token for timeout
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(3));
                    await _context.SaveChangesAsync(cts.Token);

                    _logger.LogInformation($"Created {sourcesTextEntities.Count} sources text records");
                    return sourcesTextEntities;
                }
                catch (OperationCanceledException)
                {
                    _logger.LogError("SaveChanges timeout - database too slow");
                    throw new TimeoutException("Database insert timeout");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {sourcesTextEntities?.Count ?? 0} sources text records");
                throw;
            }
        }

        public async Task<SourcesTextEntity> GetSourcesTextByIdAsync(int id)
        {
            try
            {
                // Use FindAsync for primary key - much faster
                return await _context.SourcesText.FindAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources text record with ID: {id}");
                return null; // Return null instead of throwing
            }
        }

        public async Task<List<SourcesTextEntity>> GetSourcesTextByOrganizationIdAsync(string organizationId)
        {
            try
            {
                _logger.LogInformation($"Simple query for organization: {organizationId}");

                // SUPER SIMPLE query - no ordering, no count, just basic filter
                var result = await _context.SourcesText
                    .Where(d => d.IdOrganizationFk == organizationId)
                    .Take(50) // Small limit
                    .ToListAsync();

                _logger.LogInformation($"Retrieved {result.Count} records for organization: {organizationId}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in simple query for organization: {organizationId}");
                return new List<SourcesTextEntity>(); // Return empty instead of throwing
            }
        }

        public async Task<SourcesTextEntity> UpdateSourcesTextAsync(SourcesTextEntity sourcesText)
        {
            try
            {
                var existingData = await _context.SourcesText
                    .FirstOrDefaultAsync(d => d.IdPk == sourcesText.IdPk);

                if (existingData == null)
                {
                    return null;
                }

                existingData.Title = sourcesText.Title;
                existingData.Text = sourcesText.Text;
                existingData.Timestamp = sourcesText.Timestamp;
                existingData.UpdatedAt = DateTime.UtcNow;

                _context.SaveChanges();
                
                _logger.LogInformation($"Updated sources text record with ID: {sourcesText.IdPk}");
                return existingData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating sources text record with ID: {sourcesText.IdPk}");
                throw;
            }
        }

        public async Task<bool> DeleteSourcesTextAsync(int id)
        {
            try
            {
                var data = await _context.SourcesText
                    .FirstOrDefaultAsync(d => d.IdPk == id);

                if (data == null)
                {
                    return false;
                }

                _context.SourcesText.Remove(data);
                _context.SaveChanges();
                
                _logger.LogInformation($"Deleted sources text record with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting sources text record with ID: {id}");
                throw;
            }
        }

    }
}
