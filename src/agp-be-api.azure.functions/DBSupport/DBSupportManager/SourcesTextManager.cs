using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SourcesTextManager : ISourcesTextManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SourcesTextManager> _logger;

        public SourcesTextManager(AgpDbSupportContext context, ILogger<SourcesTextManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<SourcesTextEntity>> CreateSourcesTextAsync(
            List<SourcesTextEntity> sourcesTextEntities,
            string idUser,
            string idOrganization
        )
        {
            try
            {
                if (sourcesTextEntities == null || !sourcesTextEntities.Any())
                {
                    return new List<SourcesTextEntity>();
                }

                // Set timestamps for all entities
                var now = DateTime.UtcNow;
                foreach (var entity in sourcesTextEntities)
                {
                    entity.CreatedAt = now;
                    entity.UpdatedAt = null;
                    entity.IdUserFk = idUser;
                    entity.IdOrganizationFk = idOrganization;
                }

                // Add all entities to context
                await _context.SourcesText.AddRangeAsync(sourcesTextEntities);

                // Save changes in a single transaction - this will populate the IdPk for each entity
                await _context.SaveChangesAsync();

                // Log the created IDs
                var createdIds = sourcesTextEntities.Select(e => e.IdPk).ToList();
                _logger.LogInformation($"Created {sourcesTextEntities.Count} sources text records in batch with IDs: [{string.Join(", ", createdIds)}]");

                return sourcesTextEntities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {sourcesTextEntities?.Count ?? 0} sources text records");
                throw;
            }
        }

        public async Task<SourcesTextEntity> GetSourcesTextByIdAsync(int id)
        {
            try
            {
                return await _context.SourcesText
                    .FirstOrDefaultAsync(d => d.IdPk == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources text record with ID: {id}");
                throw;
            }
        }

        public async Task<List<SourcesTextEntity>> GetSourcesTextByUserIdAsync(string userId)
        {
            try
            {
                return await _context.SourcesText
                    .Where(d => d.IdUserFk == userId)
                    .OrderByDescending(d => d.Timestamp)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources text records for user: {userId}");
                throw;
            }
        }

        public async Task<List<SourcesTextEntity>> GetSourcesTextByOrganizationIdAsync(string organizationId)
        {
            try
            {
                return await _context.SourcesText
                    .Where(d => d.IdOrganizationFk == organizationId)
                    .OrderByDescending(d => d.Timestamp)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources text records for organization: {organizationId}");
                throw;
            }
        }

        public async Task<List<SourcesTextEntity>> GetSourcesTextByUserAndOrganizationAsync(string userId, string organizationId)
        {
            try
            {
                return await _context.SourcesText
                    .Where(d => d.IdUserFk == userId && d.IdOrganizationFk == organizationId)
                    .OrderByDescending(d => d.Timestamp)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources text records for user: {userId} and organization: {organizationId}");
                throw;
            }
        }

        public async Task<SourcesTextEntity> UpdateSourcesTextAsync(SourcesTextEntity sourcesText)
        {
            try
            {
                var existingData = await _context.SourcesText
                    .FirstOrDefaultAsync(d => d.IdPk == sourcesText.IdPk);

                if (existingData == null)
                {
                    return null;
                }

                existingData.Title = sourcesText.Title;
                existingData.Text = sourcesText.Text;
                existingData.Timestamp = sourcesText.Timestamp;
                existingData.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                
                _logger.LogInformation($"Updated sources text record with ID: {sourcesText.IdPk}");
                return existingData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating sources text record with ID: {sourcesText.IdPk}");
                throw;
            }
        }

        public async Task<bool> DeleteSourcesTextAsync(int id)
        {
            try
            {
                var data = await _context.SourcesText
                    .FirstOrDefaultAsync(d => d.IdPk == id);

                if (data == null)
                {
                    return false;
                }

                _context.SourcesText.Remove(data);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation($"Deleted sources text record with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting sources text record with ID: {id}");
                throw;
            }
        }

        public async Task<List<SourcesTextEntity>> GetAllSourcesTextAsync()
        {
            try
            {
                return await _context.SourcesText
                    .OrderByDescending(d => d.Timestamp)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all sources text records");
                throw;
            }
        }
    }
}
