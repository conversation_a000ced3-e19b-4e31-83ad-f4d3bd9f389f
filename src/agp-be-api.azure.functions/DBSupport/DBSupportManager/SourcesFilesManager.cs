using System;
using System.Collections.Generic;
using System.Linq;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportContext;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using Microsoft.Extensions.Logging;

namespace AgpBeApi.Azure.Functions.DBSupport.DBSupportManager
{
    public class SourcesFilesManager : ISourcesFilesManager
    {
        private readonly AgpDbSupportContext _context;
        private readonly ILogger<SourcesFilesManager> _logger;

        public SourcesFilesManager(AgpDbSupportContext context, ILogger<SourcesFilesManager> logger)
        {
            _context = context;
            _logger = logger;
        }

        public List<SourcesFilesEntity> CreateSourcesFiles(
            List<SourcesFilesEntity> sourcesFilesEntities,
            string idUser,
            string idOrganization)
        {
            try
            {
                if (sourcesFilesEntities == null || sourcesFilesEntities.Count == 0)
                {
                    return new List<SourcesFilesEntity>();
                }

                // Set timestamps and user info for all entities (force UTC)
                var now = DateTime.UtcNow;
                foreach (var entity in sourcesFilesEntities)
                {
                    entity.CreatedAt = now;
                    entity.UpdatedAt = now;
                    entity.IdUserFk = idUser;
                    entity.IdOrganizationFk = idOrganization;
                    entity.Timestamp = now;

                    // Force UTC for any existing DateTime values
                    if (entity.Timestamp.Kind != DateTimeKind.Utc)
                    {
                        entity.Timestamp = DateTime.SpecifyKind(entity.Timestamp, DateTimeKind.Utc);
                    }
                }

                // Simple add and save (sincrono come richiesto)
                _context.SourcesFiles.AddRange(sourcesFilesEntities);
                _context.SaveChanges();

                _logger.LogInformation($"Created {sourcesFilesEntities.Count} sources files records");
                return sourcesFilesEntities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating batch of {sourcesFilesEntities?.Count ?? 0} sources files records");
                throw;
            }
        }

        public SourcesFilesEntity GetSourcesFilesByIdAsync(int id)
        {
            try
            {
                // Use Find for primary key - much faster
                return _context.SourcesFiles.Find(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving sources files record with ID: {id}");
                return null;
            }
        }

        public List<SourcesFilesEntity> GetSourcesFilesByOrganizationIdAsync(string organizationId)
        {
            try
            {
                _logger.LogInformation($"Query for organization: {organizationId}");

                // Query with ordering by IdPk DESC (newest first)
                var result = _context.SourcesFiles
                    .Where(d => d.IdOrganizationFk == organizationId)
                    .OrderByDescending(d => d.IdPk) // Dal più grande al più piccolo
                    .Take(20) // Smaller limit for files (they're bigger)
                    .ToList();

                _logger.LogInformation($"Retrieved {result.Count} files for organization: {organizationId} ordered by IdPk DESC");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in query for organization: {organizationId}");
                return new List<SourcesFilesEntity>();
            }
        }

        public SourcesFilesEntity UpdateSourcesFiles(SourcesFilesEntity sourcesFiles)
        {
            try
            {
                // Force all DateTime to UTC to avoid PostgreSQL issues
                sourcesFiles.UpdatedAt = DateTime.UtcNow;
                sourcesFiles.CreatedAt = DateTime.SpecifyKind(sourcesFiles.CreatedAt, DateTimeKind.Utc);
                sourcesFiles.Timestamp = DateTime.SpecifyKind(sourcesFiles.Timestamp, DateTimeKind.Utc);

                _context.SourcesFiles.Update(sourcesFiles);
                _context.SaveChanges();

                _logger.LogInformation($"Updated sources files record with ID: {sourcesFiles.IdPk}");
                return sourcesFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating sources files record with ID: {sourcesFiles.IdPk}");
                throw;
            }
        }

        public bool DeleteSourcesFiles(int id)
        {
            try
            {
                var entity = _context.SourcesFiles.Find(id);
                if (entity == null)
                {
                    return false;
                }

                _context.SourcesFiles.Remove(entity);
                _context.SaveChanges();
                
                _logger.LogInformation($"Deleted sources files record with ID: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting sources files record with ID: {id}");
                throw;
            }
        }
    }
}
