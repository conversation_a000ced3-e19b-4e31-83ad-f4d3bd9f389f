# AGP BE API - Project Setup Guide

This guide provides comprehensive instructions for setting up and running the AGP BE API project on both macOS Intel (for development/debugging) and Ubuntu (for production deployment).

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [macOS Intel Setup (Development)](#macos-intel-setup-development)
- [Ubuntu Setup (Production)](#ubuntu-setup-production)
- [Docker Setup (Alternative)](#docker-setup-alternative)
- [Testing the API](#testing-the-api)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### Required Software
- .NET 8 SDK
- Azure Functions Core Tools v4
- PostgreSQL 15+
- Git

### Optional (for Docker deployment)
- Docker
- Docker Compose

---

## 🍎 macOS Intel Setup (Development)

### 1. Install Prerequisites

```bash
# Install .NET 8 SDK
brew install --cask dotnet

# Verify installation
dotnet --version

# Install Azure Functions Core Tools
brew tap azure/functions
brew install azure-functions-core-tools@4

# Verify installation
func --version

# Install PostgreSQL for local testing
brew install postgresql@15
brew services start postgresql@15

# Install Git (if not already installed)
brew install git
```

### 2. Clone and Setup Project

```bash
# Clone the repository
git clone <your-repository-url>
cd agp-be-api

# Navigate to the Functions project
cd agp-be-api.azure.functions

# Restore NuGet packages
dotnet restore

# Build the project
dotnet build
```

### 3. Setup PostgreSQL Database

```bash
# Create database
createdb agp_api_db

# Run setup script
psql -d agp_api_db -f ../database-setup.sql

# Verify tables were created
psql -d agp_api_db -c "\dt"
```

### 4. Configure Environment Variables

Edit `local.settings.json`:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet",
    "ENV": "Development",
    "SWAGGER_ENABLED": "true",
    "POSTGRESQL_HOST": "localhost",
    "POSTGRESQL_PORT": "5432",
    "POSTGRESQL_DATABASE": "agp_api_db",
    "POSTGRESQL_USERNAME": "your_username",
    "POSTGRESQL_PASSWORD": "your_password",
    "REDIS_CONNECTION_STRING": "",
    "APPLICATIONINSIGHTS_CONNECTION_STRING": ""
  },
  "Host": {
    "LocalHttpPort": 7071,
    "CORS": "*",
    "CORSCredentials": false
  }
}
```

### 5. Run the Project

```bash
# Start Azure Functions
func start

# The API will be available at:
# http://localhost:7071
# Swagger UI (if enabled): http://localhost:7071/api/swagger/ui
```

### 6. Debug with Visual Studio Code

Create `.vscode/launch.json`:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Attach to .NET Functions",
            "type": "coreclr",
            "request": "attach",
            "processId": "${command:azureFunctions.pickProcess}"
        }
    ]
}
```

---

## 🐧 Ubuntu Setup (Production)

### 1. Install Prerequisites

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install .NET 8 SDK
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y dotnet-sdk-8.0

# Verify installation
dotnet --version

# Install Azure Functions Core Tools
curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > microsoft.gpg
sudo mv microsoft.gpg /etc/apt/trusted.gpg.d/microsoft.gpg
sudo sh -c 'echo "deb [arch=amd64] https://packages.microsoft.com/repos/microsoft-ubuntu-$(lsb_release -cs)-prod $(lsb_release -cs) main" > /etc/apt/sources.list.d/dotnetdev.list'
sudo apt update
sudo apt install azure-functions-core-tools-4

# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Setup PostgreSQL Database

```bash
# Switch to postgres user
sudo -u postgres psql

# Create user and database
CREATE USER agp_user WITH PASSWORD 'your_secure_password';
CREATE DATABASE agp_api_db OWNER agp_user;
GRANT ALL PRIVILEGES ON DATABASE agp_api_db TO agp_user;
\q

# Run setup script
sudo -u postgres psql -d agp_api_db -f database-setup.sql
```

### 3. Deploy Application

```bash
# Create application user
sudo useradd -r -s /bin/false agp-api

# Create application directory
sudo mkdir -p /opt/agp-be-api
sudo chown agp-api:agp-api /opt/agp-be-api

# Copy project files
sudo cp -r agp-be-api/* /opt/agp-be-api/
sudo chown -R agp-api:agp-api /opt/agp-be-api

# Build project
cd /opt/agp-be-api/agp-be-api.azure.functions
sudo -u agp-api dotnet restore
sudo -u agp-api dotnet build --configuration Release
```

### 4. Create Systemd Service

Create `/etc/systemd/system/agp-api.service`:

```ini
[Unit]
Description=AGP BE API Azure Functions
After=network.target

[Service]
Type=simple
User=agp-api
WorkingDirectory=/opt/agp-be-api/agp-be-api.azure.functions
ExecStart=/usr/bin/func start --host 0.0.0.0 --port 7071
Restart=always
RestartSec=10

# Environment Variables
Environment=AzureWebJobsStorage=UseDevelopmentStorage=true
Environment=FUNCTIONS_WORKER_RUNTIME=dotnet
Environment=ENV=Production
Environment=SWAGGER_ENABLED=false
Environment=POSTGRESQL_HOST=localhost
Environment=POSTGRESQL_PORT=5432
Environment=POSTGRESQL_DATABASE=agp_api_db
Environment=POSTGRESQL_USERNAME=agp_user
Environment=POSTGRESQL_PASSWORD=your_secure_password

[Install]
WantedBy=multi-user.target
```

### 5. Start the Service

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable and start service
sudo systemctl enable agp-api.service
sudo systemctl start agp-api.service

# Check status
sudo systemctl status agp-api.service

# View logs
sudo journalctl -u agp-api.service -f
```

---

## 🐳 Docker Setup (Alternative)

### 1. Create Dockerfile

Create `Dockerfile` in the project root:

```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY agp-be-api.azure.functions/*.csproj ./
RUN dotnet restore
COPY agp-be-api.azure.functions/ ./
RUN dotnet build --configuration Release

FROM mcr.microsoft.com/azure-functions/dotnet:4
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

COPY --from=build /src/bin/Release/net8.0 /home/<USER>/wwwroot

# Environment variables
ENV FUNCTIONS_WORKER_RUNTIME=dotnet
ENV ENV=Production
ENV SWAGGER_ENABLED=false
```

### 2. Create Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: agp_api_db
      POSTGRES_USER: agp_user
      POSTGRES_PASSWORD: your_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/init.sql

  agp-api:
    build: .
    ports:
      - "7071:80"
    environment:
      - POSTGRESQL_HOST=postgres
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=agp_api_db
      - POSTGRESQL_USERNAME=agp_user
      - POSTGRESQL_PASSWORD=your_secure_password
      - SWAGGER_ENABLED=false
    depends_on:
      - postgres

volumes:
  postgres_data:
```

### 3. Run with Docker

```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f agp-api

# Stop containers
docker-compose down
```

---

## 🧪 Testing the API

### 1. Health Check

```bash
curl -X GET "http://localhost:7071/api/v1/health" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Create Sources Text Record

```bash
curl -X POST "http://localhost:7071/api/v1/sources/text" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "idUserFk": "user123",
    "idOrganizationFk": "org456",
    "timestamp": "2024-01-01T10:00:00Z",
    "title": "Test Title",
    "text": "Test content"
  }'
```

### 3. Get Record by ID

```bash
curl -X GET "http://localhost:7071/api/v1/sources/text/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Get Records by User

```bash
curl -X GET "http://localhost:7071/api/v1/sources/text/user/user123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 5. Swagger UI

If Swagger is enabled, access: `http://localhost:7071/api/swagger/ui`

### 6. Run Unit Tests

```bash
cd agp-be-api.azure.unit-tests
dotnet test
```

---

## 🚨 Troubleshooting

### macOS Issues

#### Azure Functions Core Tools not working
```bash
brew uninstall azure-functions-core-tools@4
brew install azure-functions-core-tools@4
```

#### PostgreSQL connection issues
```bash
brew services restart postgresql@15
# Check if PostgreSQL is running
brew services list | grep postgresql
```

#### Port already in use
```bash
# Find process using port 7071
lsof -i :7071
# Kill the process
kill -9 <PID>
```

### Ubuntu Issues

#### Service not starting
```bash
# Check service status
sudo systemctl status agp-api.service

# View detailed logs
sudo journalctl -u agp-api.service -f

# Restart service
sudo systemctl restart agp-api.service
```

#### Database connection issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
sudo -u postgres psql -d agp_api_db -c "SELECT 1;"
```

#### Port issues
```bash
# Check what's using port 7071
sudo netstat -tlnp | grep :7071

# Check firewall (if applicable)
sudo ufw status
sudo ufw allow 7071
```

### Docker Issues

#### Container not starting
```bash
# Check container logs
docker-compose logs agp-api

# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

#### Database connection in Docker
```bash
# Check if containers can communicate
docker-compose exec agp-api ping postgres

# Check environment variables
docker-compose exec agp-api env | grep POSTGRESQL
```

### General Issues

#### JWT Token for Testing
For development, you can create a test JWT token using online tools like jwt.io or create a simple token generator.

#### Environment Variables
Make sure all required environment variables are set correctly:
- `POSTGRESQL_HOST`
- `POSTGRESQL_DATABASE`
- `POSTGRESQL_USERNAME`
- `POSTGRESQL_PASSWORD`

#### Build Issues
```bash
# Clean and rebuild
dotnet clean
dotnet restore
dotnet build
```

---

## 📞 Support

If you encounter issues not covered in this guide:

1. Check the application logs
2. Verify all environment variables are set correctly
3. Ensure PostgreSQL is running and accessible
4. Verify .NET 8 SDK and Azure Functions Core Tools are properly installed
5. Check firewall settings if running on a server

For additional help, refer to the main README.md file or contact the development team.

---

## 🔐 Security Notes

### JWT Token Requirements
- All API endpoints require a valid JWT token in the Authorization header
- The token must contain a valid UPN (User Principal Name)
- Format: `Authorization: Bearer <your-jwt-token>`

### Production Security
- Change default passwords
- Use strong passwords for database users
- Configure proper firewall rules
- Use HTTPS in production
- Disable Swagger in production (`SWAGGER_ENABLED=false`)

---

## 📊 Monitoring and Logging

### Application Insights (Optional)
Set the `APPLICATIONINSIGHTS_CONNECTION_STRING` environment variable to enable Application Insights monitoring.

### Log Locations

#### macOS (Development)
- Function logs: Console output
- System logs: `/var/log/system.log`

#### Ubuntu (Production)
- Service logs: `sudo journalctl -u agp-api.service`
- PostgreSQL logs: `/var/log/postgresql/`

#### Docker
- Container logs: `docker-compose logs agp-api`
- PostgreSQL logs: `docker-compose logs postgres`

---

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart (Ubuntu)
cd /opt/agp-be-api/agp-be-api.azure.functions
sudo -u agp-api dotnet build --configuration Release
sudo systemctl restart agp-api.service

# Or with Docker
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Migrations
When database schema changes are needed:
1. Update the `database-setup.sql` file
2. Create migration scripts
3. Apply changes to the database
4. Test thoroughly before deploying

---

## 🌐 Environment-Specific Configurations

### Development
- Swagger enabled
- Detailed logging
- Local PostgreSQL
- CORS enabled for all origins

### Staging
- Swagger disabled
- Moderate logging
- Shared PostgreSQL instance
- Restricted CORS

### Production
- Swagger disabled
- Error logging only
- Production PostgreSQL cluster
- Strict CORS policy
- Application Insights enabled

---

## 📋 Checklist

### Before First Run
- [ ] .NET 8 SDK installed
- [ ] Azure Functions Core Tools installed
- [ ] PostgreSQL installed and running
- [ ] Database created and initialized
- [ ] Environment variables configured
- [ ] Project built successfully

### Before Production Deployment
- [ ] Security review completed
- [ ] Environment variables secured
- [ ] Database backups configured
- [ ] Monitoring setup
- [ ] Load testing completed
- [ ] Documentation updated

---

## 🤝 Contributing

When contributing to this project:
1. Follow the existing code structure
2. Add appropriate unit tests
3. Update documentation
4. Test on both macOS and Ubuntu
5. Ensure Docker setup works

---

## 📚 Additional Resources

- [Azure Functions Documentation](https://docs.microsoft.com/en-us/azure/azure-functions/)
- [.NET 8 Documentation](https://docs.microsoft.com/en-us/dotnet/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)

---

*Last updated: January 2024*
