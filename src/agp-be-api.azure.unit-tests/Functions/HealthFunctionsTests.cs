using AgpBeApi.Azure.Functions.DTO;
using AgpBeApi.Azure.Functions.Functions;
using AgpBeApi.Azure.Functions.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;

namespace AgpBeApi.Azure.UnitTests.Functions
{
    public class HealthFunctionsTests
    {
        private readonly Mock<IHealthService> _mockHealthService;
        private readonly Mock<ILogger> _mockLogger;
        private readonly HealthFunctions _healthFunctions;

        public HealthFunctionsTests()
        {
            _mockHealthService = new Mock<IHealthService>();
            _mockLogger = new Mock<ILogger>();
            _healthFunctions = new HealthFunctions(_mockHealthService.Object);
        }

        [Fact]
        public async Task GetHealth_ShouldReturnOkResult_WhenHealthServiceSucceeds()
        {
            // Arrange
            var expectedHealthStatus = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0.0",
                Environment = "Test",
                Database = "Healthy",
                SwaggerEnabled = true
            };

            _mockHealthService.Setup(x => x.GetHealthStatusAsync())
                             .ReturnsAsync(expectedHealthStatus);

            var mockRequest = new Mock<HttpRequest>();

            // Act
            var result = await _healthFunctions.GetHealth(mockRequest.Object, _mockLogger.Object);

            // Assert
            Assert.IsType<OkObjectResult>(result);
            var okResult = result as OkObjectResult;
            Assert.NotNull(okResult);
            
            var returnMessage = okResult.Value as ReturnMessage<object>;
            Assert.NotNull(returnMessage);
            Assert.Equal("OK", returnMessage.Status);
            Assert.Equal("Health check completed", returnMessage.Message);
            Assert.Equal(expectedHealthStatus, returnMessage.Data);
        }

        [Fact]
        public async Task GetHealth_ShouldReturnErrorResult_WhenHealthServiceThrows()
        {
            // Arrange
            _mockHealthService.Setup(x => x.GetHealthStatusAsync())
                             .ThrowsAsync(new Exception("Database connection failed"));

            var mockRequest = new Mock<HttpRequest>();

            // Act
            var result = await _healthFunctions.GetHealth(mockRequest.Object, _mockLogger.Object);

            // Assert
            Assert.IsType<ObjectResult>(result);
            var objectResult = result as ObjectResult;
            Assert.NotNull(objectResult);
            Assert.Equal(500, objectResult.StatusCode);
            
            var returnMessage = objectResult.Value as ReturnMessage<object>;
            Assert.NotNull(returnMessage);
            Assert.Equal("ERROR", returnMessage.Status);
            Assert.Equal("Health check failed", returnMessage.Message);
            Assert.Equal("HEALTH_CHECK_ERROR", returnMessage.ErrorCode);
        }
    }
}
