using AgpBeApi.Azure.Functions.DBSupport.DBSupportEntities;
using AgpBeApi.Azure.Functions.DBSupport.DBSupportManager.Interfaces;
using AgpBeApi.Azure.Functions.DTO;
using AgpBeApi.Azure.Functions.Services;
using AgpBeApi.Azure.Functions.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace AgpBeApi.Azure.UnitTests.Services
{
    public class SourcesTextServiceTests
    {
        private readonly Mock<ISourcesTextManager> _mockSourcesTextManager;
        private readonly Mock<ILogger<SourcesTextService>> _mockLogger;
        private readonly SourcesTextService _sourcesTextService;

        public SourcesTextServiceTests()
        {
            _mockSourcesTextManager = new Mock<ISourcesTextManager>();
            _mockLogger = new Mock<ILogger<SourcesTextService>>();
            _sourcesTextService = new SourcesTextService(_mockSourcesTextManager.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task CreateSourcesTextAsync_ShouldReturnSourcesTextDto_WhenValidInput()
        {
            // Arrange
            var createSourcesTextDto = new CreateSourcesTextDto
            {
                IdUserFk = "user123",
                IdOrganizationFk = "org456",
                Timestamp = DateTime.UtcNow,
                Title = "Test Title",
                Text = "Test Content"
            };

            var expectedEntity = new SourcesTextEntity
            {
                IdPk = 1,
                IdUserFk = createSourcesTextDto.IdUserFk,
                IdOrganizationFk = createSourcesTextDto.IdOrganizationFk,
                Timestamp = createSourcesTextDto.Timestamp,
                Title = createSourcesTextDto.Title,
                Text = createSourcesTextDto.Text,
                CreatedAt = DateTime.UtcNow
            };

            _mockSourcesTextManager.Setup(x => x.CreateSourcesTextAsync(It.IsAny<SourcesTextEntity>()))
                                  .ReturnsAsync(expectedEntity);

            // Act
            var result = await _sourcesTextService.CreateSourcesTextAsync(createSourcesTextDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedEntity.IdPk, result.IdPk);
            Assert.Equal(expectedEntity.IdUserFk, result.IdUserFk);
            Assert.Equal(expectedEntity.IdOrganizationFk, result.IdOrganizationFk);
            Assert.Equal(expectedEntity.Title, result.Title);
            Assert.Equal(expectedEntity.Text, result.Text);
        }

        [Fact]
        public async Task GetSourcesTextByIdAsync_ShouldReturnSourcesTextDto_WhenEntityExists()
        {
            // Arrange
            var entityId = 1;
            var expectedEntity = new SourcesTextEntity
            {
                IdPk = entityId,
                IdUserFk = "user123",
                IdOrganizationFk = "org456",
                Timestamp = DateTime.UtcNow,
                Title = "Test Title",
                Text = "Test Content",
                CreatedAt = DateTime.UtcNow
            };

            _mockSourcesTextManager.Setup(x => x.GetSourcesTextByIdAsync(entityId))
                                  .ReturnsAsync(expectedEntity);

            // Act
            var result = await _sourcesTextService.GetSourcesTextByIdAsync(entityId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedEntity.IdPk, result.IdPk);
            Assert.Equal(expectedEntity.IdUserFk, result.IdUserFk);
        }

        [Fact]
        public async Task GetSourcesTextByIdAsync_ShouldReturnNull_WhenEntityDoesNotExist()
        {
            // Arrange
            var entityId = 999;
            _mockSourcesTextManager.Setup(x => x.GetSourcesTextByIdAsync(entityId))
                                  .ReturnsAsync((SourcesTextEntity)null);

            // Act
            var result = await _sourcesTextService.GetSourcesTextByIdAsync(entityId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetSourcesTextByUserIdAsync_ShouldReturnListOfSourcesTextDto()
        {
            // Arrange
            var userId = "user123";
            var entities = new List<SourcesTextEntity>
            {
                new SourcesTextEntity
                {
                    IdPk = 1,
                    IdUserFk = userId,
                    IdOrganizationFk = "org456",
                    Timestamp = DateTime.UtcNow,
                    Title = "Test Title 1",
                    Text = "Test Content 1",
                    CreatedAt = DateTime.UtcNow
                },
                new SourcesTextEntity
                {
                    IdPk = 2,
                    IdUserFk = userId,
                    IdOrganizationFk = "org789",
                    Timestamp = DateTime.UtcNow,
                    Title = "Test Title 2",
                    Text = "Test Content 2",
                    CreatedAt = DateTime.UtcNow
                }
            };

            _mockSourcesTextManager.Setup(x => x.GetSourcesTextByUserIdAsync(userId))
                                  .ReturnsAsync(entities);

            // Act
            var result = await _sourcesTextService.GetSourcesTextByUserIdAsync(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.All(result, dto => Assert.Equal(userId, dto.IdUserFk));
        }

        [Fact]
        public async Task DeleteSourcesTextAsync_ShouldReturnTrue_WhenEntityExists()
        {
            // Arrange
            var entityId = 1;
            _mockSourcesTextManager.Setup(x => x.DeleteSourcesTextAsync(entityId))
                                  .ReturnsAsync(true);

            // Act
            var result = await _sourcesTextService.DeleteSourcesTextAsync(entityId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteSourcesTextAsync_ShouldReturnFalse_WhenEntityDoesNotExist()
        {
            // Arrange
            var entityId = 999;
            _mockSourcesTextManager.Setup(x => x.DeleteSourcesTextAsync(entityId))
                                  .ReturnsAsync(false);

            // Act
            var result = await _sourcesTextService.DeleteSourcesTextAsync(entityId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CreateSourcesTextAsync_ShouldThrowBadRequestException_WhenDtoIsNull()
        {
            // Act & Assert
            await Assert.ThrowsAsync<BadRequestException>(() =>
                _sourcesTextService.CreateSourcesTextAsync(null));
        }

        [Fact]
        public async Task CreateSourcesTextAsync_ShouldThrowBadRequestException_WhenIdUserFkIsEmpty()
        {
            // Arrange
            var createSourcesTextDto = new CreateSourcesTextDto
            {
                IdUserFk = "",
                IdOrganizationFk = "org456",
                Timestamp = DateTime.UtcNow,
                Title = "Test Title",
                Text = "Test Content"
            };

            // Act & Assert
            await Assert.ThrowsAsync<BadRequestException>(() =>
                _sourcesTextService.CreateSourcesTextAsync(createSourcesTextDto));
        }

        [Fact]
        public async Task GetSourcesTextByIdAsync_ShouldThrowBadRequestException_WhenIdIsZero()
        {
            // Act & Assert
            await Assert.ThrowsAsync<BadRequestException>(() =>
                _sourcesTextService.GetSourcesTextByIdAsync(0));
        }

        [Fact]
        public async Task GetSourcesTextByIdAsync_ShouldThrowNotFoundException_WhenEntityNotFound()
        {
            // Arrange
            var entityId = 999;
            _mockSourcesTextManager.Setup(x => x.GetSourcesTextByIdAsync(entityId))
                                  .ReturnsAsync((SourcesTextEntity)null);

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _sourcesTextService.GetSourcesTextByIdAsync(entityId));
        }
    }
}
