using Microsoft.AspNetCore.Mvc;

namespace AgpBeApi.Azure.Docs.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DocumentationController : ControllerBase
    {
        /// <summary>
        /// Get API documentation information
        /// </summary>
        /// <returns>API documentation details</returns>
        [HttpGet]
        public IActionResult GetDocumentation()
        {
            var documentation = new
            {
                Title = "AGP BE API Documentation",
                Version = "1.0.0",
                Description = "REST API for sources text management operations with PostgreSQL backend",
                BaseUrl = "https://your-function-app.azurewebsites.net",
                Endpoints = new[]
                {
                    new { Method = "GET", Path = "/api/v1/health", Description = "Get API health status" },
                    new { Method = "POST", Path = "/api/v1/sources/text", Description = "Create a new sources text record" },
                    new { Method = "GET", Path = "/api/v1/sources/text/{id}", Description = "Get sources text record by ID" },
                    new { Method = "PUT", Path = "/api/v1/sources/text/{id}", Description = "Update sources text record" },
                    new { Method = "DELETE", Path = "/api/v1/sources/text/{id}", Description = "Delete sources text record" },
                    new { Method = "GET", Path = "/api/v1/sources/text/user/{userId}", Description = "Get sources text records by user ID" },
                    new { Method = "GET", Path = "/api/v1/sources/text/organization/{organizationId}", Description = "Get sources text records by organization ID" },
                    new { Method = "GET", Path = "/api/v1/sources/text/user/{userId}/organization/{organizationId}", Description = "Get sources text records by user and organization ID" }
                },
                DatabaseSchema = new
                {
                    TableName = "sources_text",
                    Fields = new[]
                    {
                        new { Name = "id_pk", Type = "int", Description = "Primary key, auto-increment" },
                        new { Name = "id_user_fk", Type = "varchar(255)", Description = "User foreign key, indexed" },
                        new { Name = "id_organization_fk", Type = "varchar(255)", Description = "Organization foreign key, indexed" },
                        new { Name = "timestamp", Type = "timestamp", Description = "Record timestamp, indexed" },
                        new { Name = "title", Type = "varchar(2000)", Description = "Record title" },
                        new { Name = "text", Type = "text", Description = "Record content" },
                        new { Name = "created_at", Type = "timestamp", Description = "Creation timestamp" },
                        new { Name = "updated_at", Type = "timestamp", Description = "Last update timestamp" }
                    }
                }
            };

            return Ok(documentation);
        }
    }
}
