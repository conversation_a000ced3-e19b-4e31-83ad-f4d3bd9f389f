-- Create the sources_files table
CREATE TABLE IF NOT EXISTS sources_files (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    id_organization_fk VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    file_name VARCHAR(500) NOT NULL,
    file_content TEXT NOT NULL,  -- Base64 string (max 1GB)
    file_size BIGINT NOT NULL DEFAULT 0,
    mime_type VARCHAR(200),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Verify table ccreated
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'sources_files'
ORDER BY ordinal_position;
