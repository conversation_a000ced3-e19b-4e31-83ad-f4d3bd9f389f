-- Create the sources_qa_questions table
CREATE TABLE IF NOT EXISTS sources_qa_questions (
    id_pk SERIAL PRIMARY KEY,
    text TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create the sources_qa_questions table
CREATE TABLE IF NOT EXISTS sources_qa (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    id_organization_fk VARCHAR(50) NOT NULL,
    questions_ids_fk TEXT NOT NULL,  -- IDs separati da virgola (es: "1,2,3")
    title VARCHAR(2000) NOT NULL,
    answer TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Verify sources_qa_questions
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sources_qa_questions'
ORDER BY ordinal_position;

-- Verify sources_qa
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sources_qa'
ORDER BY ordinal_position;
