-- Create the settings_ai table
CREATE TABLE IF NOT EXISTS settings_ai (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    id_organization_fk VARCHAR(50) NOT NULL,
    model_name VARCHAR(200) NOT NULL,
    instruction TEXT NOT NULL,  -- Long text for AI prompt with formatting
    temperature DECIMAL(3,2) NOT NULL DEFAULT 0.7,  -- Range 0.0 to 1.0
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Verify settings_ai table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'settings_ai'
ORDER BY ordinal_position;
