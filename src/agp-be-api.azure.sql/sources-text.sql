-- AGP BE API Database Setup Script for PostgreSQL
-- This script creates the necessary table and indexes for the AGP BE API

-- Create the database (run this separately if needed)
-- CREATE DATABASE agp_api_db;

-- Connect to the database
-- \c agp_api_db;

-- Create the sources_text table
CREATE TABLE IF NOT EXISTS sources_text (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(255) NOT NULL,
    id_organization_fk VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    title VARCHAR(2000) NOT NULL,
    text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS IX_SourcesText_IdUserFk
ON sources_text (id_user_fk);

CREATE INDEX IF NOT EXISTS IX_SourcesText_IdOrganizationFk
ON sources_text (id_organization_fk);

CREATE INDEX IF NOT EXISTS IX_SourcesText_IdUserFk_IdOrganizationFk
ON sources_text (id_user_fk, id_organization_fk);

CREATE INDEX IF NOT EXISTS IX_SourcesText_Timestamp
ON sources_text (timestamp);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a trigger to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_sources_text_updated_at ON sources_text;
CREATE TRIGGER update_sources_text_updated_at
    BEFORE UPDATE ON sources_text
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Show sample data
SELECT * FROM sources_text LIMIT 5;
