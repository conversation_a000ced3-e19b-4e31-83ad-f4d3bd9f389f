-- Create the sources_text table
CREATE TABLE IF NOT EXISTS sources_text (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(255) NOT NULL,
    id_organization_fk VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    title VARCHAR(2000) NOT NULL,
    text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Verify table ccreated
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'sources_text'
ORDER BY ordinal_position;
