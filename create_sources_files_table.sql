-- Script per creare la tabella sources_files e i relativi indici

-- Creazione tabella sources_files
CREATE TABLE IF NOT EXISTS sources_files (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    id_organization_fk VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    file_type VARCHAR(100),
    mime_type VARCHAR(200),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_sources_files_organization 
ON sources_files (id_organization_fk);

CREATE INDEX IF NOT EXISTS idx_sources_files_user 
ON sources_files (id_user_fk);

CREATE INDEX IF NOT EXISTS idx_sources_files_org_timestamp 
ON sources_files (id_organization_fk, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_sources_files_user_org 
ON sources_files (id_user_fk, id_organization_fk);

CREATE INDEX IF NOT EXISTS idx_sources_files_filename 
ON sources_files (file_name);

CREATE INDEX IF NOT EXISTS idx_sources_files_type 
ON sources_files (file_type);

-- Trigger per aggiornare updated_at automaticamente
CREATE OR REPLACE FUNCTION update_sources_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sources_files_updated_at
    BEFORE UPDATE ON sources_files
    FOR EACH ROW
    EXECUTE FUNCTION update_sources_files_updated_at();

-- Verifica la struttura creata
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sources_files'
ORDER BY ordinal_position;

-- Verifica gli indici creati
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'sources_files'
ORDER BY indexname;
