-- Script per creare le tabelle sources_qa e sources_qa_questions

-- Creazione tabella sources_qa_questions (prima perché referenziata)
CREATE TABLE IF NOT EXISTS sources_qa_questions (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    text TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Creazione tabella sources_qa
CREATE TABLE IF NOT EXISTS sources_qa (
    id_pk SERIAL PRIMARY KEY,
    id_user_fk VARCHAR(50) NOT NULL,
    id_organization_fk VARCHAR(50) NOT NULL,
    questions_ids_fk TEXT NOT NULL,  -- IDs separati da virgola (es: "1,2,3")
    title VARCHAR(2000) NOT NULL,
    answer TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Verifica la struttura creata per sources_qa_questions
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sources_qa_questions'
ORDER BY ordinal_position;

-- Verifica la struttura creata per sources_qa
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sources_qa'
ORDER BY ordinal_position;
