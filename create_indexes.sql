-- Script per creare gli indici necessari per migliorare le performance

-- Indice su IdOrganizationFk (molto importante per le query GET)
CREATE INDEX IF NOT EXISTS idx_sources_text_organization 
ON sources_text (id_organization_fk);

-- Indice su IdUserFk 
CREATE INDEX IF NOT EXISTS idx_sources_text_user 
ON sources_text (id_user_fk);

-- Indice composito per query che filtrano per organization e ordinano per timestamp
CREATE INDEX IF NOT EXISTS idx_sources_text_org_timestamp 
ON sources_text (id_organization_fk, timestamp DESC);

-- Indice composito per query che filtrano per user e organization
CREATE INDEX IF NOT EXISTS idx_sources_text_user_org 
ON sources_text (id_user_fk, id_organization_fk);

-- Verifica gli indici creati
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'sources_text'
ORDER BY indexname;
